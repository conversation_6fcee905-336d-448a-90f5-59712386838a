import { isAuth } from "../middleware/isAuth";
import {
  Arg,
  Mutation,
  Resolver,
  UseMiddleware,
  Query,
  InputType,
  Field,
  ObjectType,
  Int,
  Ctx,
  registerEnumType,
} from "type-graphql";
import { Schedule } from "../entities/Schedule";
import { BooleanResponse } from "./user";
import { Clinic } from "../entities/Clinic";
import { Employee } from "../entities/Employee";
import { MyContext } from "../types";
import { Between, getConnection } from "typeorm";
import { User } from "../entities/User";
import { logError } from "../utils/utils";

enum DayOfWeek {
  MONDAY = "MONDAY",
  TUESDAY = "TUESDAY",
  WEDNESDAY = "WEDNESDAY",
  THURSDAY = "THURSDAY",
  FRIDAY = "FRIDAY",
  SATURDAY = "SATURDAY",
  SUNDAY = "SUNDAY",
}

enum Status {
  POSTPONED = "postponed",
  CANCELED = "canceled",
  ATTENDED = "attended",
  MISSED = "missed",
  PENDING = "pending",
}
registerEnumType(Status, { name: "Status" });
registerEnumType(DayOfWeek, { name: "DayOfWeek" });

@InputType()
class ScheduleDetailsArgs {
  @Field()
  onTime: string;

  @Field()
  offTime: string;

  @Field(() => String)
  day: DayOfWeek;

  @Field(() => String, { nullable: true })
  description?: string;
}

@InputType()
class ScheduleBulkArgs {
  @Field({ nullable: true })
  clinicId?: number;

  @Field({ nullable: true })
  employeeId?: number;

  @Field(() => [ScheduleDetailsArgs])
  schedules: ScheduleDetailsArgs[]; // Array of schedule input arguments
}

@InputType()
class ScheduleEditDetailsArgs {
  @Field()
  scheduleId: number;

  @Field()
  onTime: string;

  @Field()
  offTime: string;

  @Field(() => String)
  description: string;
}

@InputType()
class ScheduleEditBulkArgs {
  @Field(() => [ScheduleEditDetailsArgs])
  schedules: ScheduleEditDetailsArgs[]; // Array of schedule input arguments
}

@ObjectType()
export class BulkScheduleResponse extends BooleanResponse {
  @Field(() => [Schedule], { nullable: true })
  schedules?: Schedule[];
}

@ObjectType()
export class ScheduleResponse extends BooleanResponse {
  @Field(() => Schedule, { nullable: true })
  schedule?: Schedule;
}

@Resolver(Schedule)
export class ScheduleResolver {
  @Mutation(() => BulkScheduleResponse)
  @UseMiddleware(isAuth)
  async addSchedules(
    @Arg("args") inputArgs: ScheduleBulkArgs,
    @Ctx() { req }: MyContext
  ): Promise<BulkScheduleResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    const schedules: Schedule[] = [];
    const clinic = await Clinic.findOne({
      where: { id: inputArgs.clinicId, companyId: req.session.companyId },
    });
    const employee = await Employee.findOne({
      where: { id: inputArgs.employeeId, companyId: req.session.companyId },
    });

    if (!clinic && !employee) {
      logError(
        companyId,
        "Schedule target not found",
        "SCHEDULE_ADD_TARGET_NOT_FOUND",
        JSON.stringify(inputArgs),
        "medium",
        "Add schedules failed - neither clinic nor employee found",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Schedule target not found.",
        },
      };
    }

    try {
      if (clinic) {
        for (const scheduleInput of inputArgs.schedules) {
          const newSchedule = await Schedule.create({
            onTime: scheduleInput.onTime,
            offTime: scheduleInput.offTime,
            day: scheduleInput.day,
            clinicId: clinic.id,
            description: scheduleInput.description,
            companyId: req.session.companyId,
          }).save();

          schedules.push(newSchedule);
        }
      }

      if (employee) {
        for (const scheduleInput of inputArgs.schedules) {
          const newSchedule = await Schedule.create({
            onTime: scheduleInput.onTime,
            offTime: scheduleInput.offTime,
            day: scheduleInput.day,
            employeeId: employee.id,
            description: scheduleInput.description,
            companyId: req.session.companyId,
          }).save();

          schedules.push(newSchedule);
        }
      }
      return {
        status: true,
        schedules,
      };
    } catch (error) {
      logError(
        companyId,
        error.message,
        "SCHEDULE_ADD_ERROR",
        JSON.stringify({ error, inputArgs }),
        "high",
        "Failed to add schedules",
        userId
      );
      console.error("Error adding schedules: ", error.message);
      return {
        status: false,
        error: {
          target: "general",
          message: error.message,
        },
      };
    }
  }

  @Mutation(() => BulkScheduleResponse)
  @UseMiddleware(isAuth)
  async editSchedules(
    @Arg("args") inputArgs: ScheduleEditBulkArgs,
    @Ctx() { req }: MyContext
  ): Promise<BulkScheduleResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    const updatedSchedules: Schedule[] = [];
    try {
      for (const scheduleInput of inputArgs.schedules) {
        const schedule = await Schedule.findOne({
          where: {
            id: scheduleInput.scheduleId,
            companyId: req.session.companyId,
          },
        });

        if (!schedule) {
          logError(
            companyId,
            "Schedule not found",
            "SCHEDULE_EDIT_NOT_FOUND",
            JSON.stringify({ scheduleId: scheduleInput.scheduleId }),
            "medium",
            `Edit schedule failed - schedule not found: ${scheduleInput.scheduleId}`,
            userId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: `Schedule with ID ${scheduleInput.scheduleId} not found.`,
            },
          };
        }

        // Update the schedule fields
        schedule.onTime = scheduleInput.onTime;
        schedule.offTime = scheduleInput.offTime;
        schedule.description = scheduleInput.description;

        const updatedSchedule = await schedule.save();
        updatedSchedules.push(updatedSchedule);
      }

      return {
        status: true,
        schedules: updatedSchedules,
      };
    } catch (error) {
      logError(
        companyId,
        error.message,
        "SCHEDULE_EDIT_ERROR",
        JSON.stringify({ error, inputArgs }),
        "high",
        "Failed to edit schedules",
        userId
      );
      console.error("Error editing schedules: ", error.message);
      return {
        status: false,
        error: {
          target: "general",
          message: error.message,
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteSchedule(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    try {
      const schedule = await Schedule.findOne({
        where: { id, companyId: req.session.companyId },
      });
      if (!schedule) {
        logError(
          companyId,
          "Schedule not found",
          "SCHEDULE_DELETE_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Delete schedule failed - schedule not found: ${id}`,
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Schedule not found" },
        };
      }

      await Schedule.delete({ id, companyId: req.session.companyId });
      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SCHEDULE_DELETE_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        `Failed to delete schedule: ${id}`,
        userId
      );
      console.error("Error deleting schedule: ", err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Query(() => [Schedule])
  @UseMiddleware(isAuth)
  async getSchedules(
    @Arg("ownerId", () => Int) ownerId: number,
    @Arg("owner", () => String) owner: "clinic" | "employee",
    @Ctx() { req }: MyContext
  ): Promise<Schedule[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    let schedules;

    try {
      if (owner === "clinic") {
        schedules = await Schedule.find({
          where: { clinicId: ownerId, companyId: req.session.companyId },
          relations: ["clinic", "employee"],
        });
      } else if (owner === "employee") {
        schedules = await Schedule.find({
          where: { employeeId: ownerId, companyId: req.session.companyId },
          relations: ["clinic", "employee"],
        });
      } else {
        logError(
          companyId,
          "Invalid owner type",
          "SCHEDULE_GET_INVALID_OWNER",
          JSON.stringify({ owner, ownerId }),
          "medium",
          `Get schedules failed - invalid owner type: ${owner}`,
          userId
        );
        throw new Error('Invalid owner type. Must be "clinic" or "employee".');
      }

      return schedules;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SCHEDULE_GET_ERROR",
        JSON.stringify({ error: err, owner, ownerId }),
        "high",
        "Failed to fetch schedules",
        userId
      );
      throw err;
    }
  }

  @Query(() => [Schedule])
  @UseMiddleware(isAuth)
  async schedules(
    @Arg("startDate") startDate: Date,
    @Arg("endDate") endDate: Date,
    @Ctx() { req }: MyContext,
    @Arg("clinicId", { nullable: true }) clinicId?: number
  ): Promise<Schedule[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    const whereClause: any = {
      companyId: req.session.companyId,
      deleted: false,
      appointmentDate: Between(startDate, endDate),
    };

    if (clinicId) {
      whereClause.clinicId = clinicId;
    }

    try {
      return Schedule.find({
        where: whereClause,
        relations: ["clinic", "clinic.doctor", "patient"],
        order: { appointmentDate: "ASC" },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SCHEDULES_QUERY_ERROR",
        JSON.stringify({ error: err, startDate, endDate, clinicId }),
        "high",
        "Failed to fetch schedules by date range",
        userId
      );
      throw err;
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async updateScheduleStatus(
    @Arg("scheduleId") scheduleId: number,
    @Arg("status") status: Status,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    try {
      const schedule = await Schedule.findOne({
        where: {
          id: scheduleId,
          companyId: req.session.companyId,
        },
      });

      if (!schedule) {
        logError(
          companyId,
          "Schedule not found",
          "SCHEDULE_STATUS_UPDATE_NOT_FOUND",
          JSON.stringify({ scheduleId }),
          "medium",
          `Update schedule status failed - schedule not found: ${scheduleId}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "schedule",
            message: "Schedule not found",
          },
        };
      }

      schedule.status = status;
      await schedule.save();

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SCHEDULE_STATUS_UPDATE_ERROR",
        JSON.stringify({ error: err, scheduleId, status }),
        "high",
        `Failed to update schedule status: ${scheduleId}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async cancelSchedule(
    @Arg("scheduleId") scheduleId: number,
    @Ctx() { req }: MyContext,
    @Arg("reason", { nullable: true }) reason?: string
  ): Promise<BooleanResponse> {
    const userId = req.session.userId;
    const companyId = req.session.companyId;

    try {
      await getConnection().transaction(async (tm) => {
        const schedule = await tm.findOne(Schedule, {
          where: { id: scheduleId, companyId, deleted: false },
          relations: ["clinic", "clinic.doctor", "patient"],
        });

        if (!schedule) {
          logError(
            companyId,
            "Schedule not found",
            "SCHEDULE_CANCEL_NOT_FOUND",
            JSON.stringify({ scheduleId }),
            "medium",
            `Cancel schedule failed - schedule not found: ${scheduleId}`,
            userId
          );
          throw new Error("Schedule not found");
        }

        const employee = await tm.findOne(Employee, { where: { userId } });
        const admin = await tm.findOne(User, {
          where: { id: userId },
          relations: ["role"],
        });

        const isDoctor = schedule.clinic.leader.userId === userId;

        if (!isDoctor && !employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized cancellation attempt",
            "SCHEDULE_CANCEL_UNAUTHORIZED",
            JSON.stringify({ scheduleId, userId }),
            "medium",
            `Cancel schedule failed - unauthorized user: ${userId}`,
            userId
          );
          throw new Error("Not authorized to cancel schedule");
        }
        schedule.status = Status.CANCELED;
        schedule.description = `Cancellation reason: ${reason}`;
        await schedule.save();
      });

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SCHEDULE_CANCEL_ERROR",
        JSON.stringify({ error: err, scheduleId, reason }),
        "high",
        `Failed to cancel schedule: ${scheduleId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }
}
