import "reflect-metadata";
import https from "https";
import fs from "fs";
import express from "express";
import { ApolloServer } from "apollo-server-express";
import { buildSchema } from "type-graphql";
import Redis from "ioredis";
import session from "express-session";
import connectRedis from "connect-redis";
import cors from "cors";
import path from "path";
import { createConnection } from "typeorm";
import {
  COOKIE_NAME,
  customSecret,
  DB_NAME,
  DB_PASSWORD,
  DB_TYPE,
  DB_USER,
  __prod__,
} from "./constants.js";
import { RoleResolver } from "./resolvers/role";
import { UserResolver } from "./resolvers/user";
import { PermissionResolver } from "./resolvers/permission";
import { FeatureResolver } from "./resolvers/feature";
import { CategoryResolver } from "./resolvers/category";
import { TypeResolver } from "./resolvers/type";
import { CompanyResolver } from "./resolvers/company";
import { InventoryResolver } from "./resolvers/inventory";
import { PharmacyResolver } from "./resolvers/pharmacy";
import { ExpenseResolver } from "./resolvers/Expense";
import { BillResolver } from "./resolvers/billing";
import scheduler from "./utils/scheduler.js";
import { PatientResolver } from "./resolvers/patient.js";
import { DepartmentResolver } from "./resolvers/department.js";
import { ClinicResolver } from "./resolvers/clinic.js";
import { ScheduleResolver } from "./resolvers/schedule.js";
import { SyncResolver } from "./resolvers/sync.js";
import { verifyToken } from "./utils/jwt";
import { MessageResolver } from "./resolvers/message";
import { LogResolver } from "./resolvers/log.js";
import { PaymentResolver } from "./resolvers/Payment.js";

const httpsOptions = {
  key: fs.readFileSync(
    path.join(__dirname, "../certificates/localhost-key.pem")
  ),
  cert: fs.readFileSync(path.join(__dirname, "../certificates/localhost.pem")),
};

const main = async () => {
  const conn = await createConnection({
    type: DB_TYPE,
    database: DB_NAME,
    username: DB_USER,
    password: DB_PASSWORD,
    logging: true,
    synchronize: true,
    entities: [
      path.join(__dirname, "./entities/*"),
      path.join(__dirname, "./entities/Inventory/*"),
      path.join(__dirname, "./entities/Patient/*"),
    ],
    migrations: [path.join(__dirname, "./migrations/*")],
    cli: {
      migrationsDir: "./migrations",
    },
  });

  if (!conn.isConnected) {
    console.error("Failed to connect to the database");
    return;
  }

  console.log("Connected to db:", conn.name);

  const webApp = express();
  const desktopApp = express();

  const RedisStore = connectRedis(session as any);

  const redis = new Redis();

  webApp.use(
    session({
      name: COOKIE_NAME,
      store: new (RedisStore as any)({ client: redis }),
      cookie: {
        maxAge: 1000 * 60 * 60 * 24 * 365 * 10,
        httpOnly: true,
        sameSite: "none",
        secure: true,
      },
      secret: customSecret!,
      resave: false,
      saveUninitialized: true,
    })
  );

  desktopApp.use(
    session({
      name: COOKIE_NAME,
      store: new (RedisStore as any)({ client: redis }),
      cookie: {
        maxAge: 1000 * 60 * 60 * 24 * 365 * 10,
        httpOnly: true,
        sameSite: "none",
        secure: true,
      },
      secret: customSecret!,
      resave: false,
      saveUninitialized: true,
    })
  );

  webApp.use(
    cors({
      origin: [
        "http://localhost:3000",
        "http://************:8081",
        "http://localhost:8081",
      ],
      credentials: true,
    })
  );
  desktopApp.use(
    cors({
      origin: [
        "https://localhost:5123",
        "https://************:8081",
        "http://************:8081",
        "https://localhost:8081",
        "http://localhost:8081",
        "exp://************:8081",
      ],
      credentials: true,
    })
  );

  const apolloWebServer = new ApolloServer({
    schema: await buildSchema({
      resolvers: [
        LogResolver,
        UserResolver,
        RoleResolver,
        PermissionResolver,
        CategoryResolver,
        TypeResolver,
        CompanyResolver,
        FeatureResolver,
        InventoryResolver,
        PharmacyResolver,
        ExpenseResolver,
        BillResolver,
        PatientResolver,
        DepartmentResolver,
        ClinicResolver,
        ScheduleResolver,
        SyncResolver,
        MessageResolver,
        PaymentResolver,
      ],
      validate: true,
      emitSchemaFile: true,
    }),
    context: ({ req, res }) => {
      // Extract JWT from Authorization header
      const token = req.headers.authorization?.split(" ")[1];
      if (token) {
        const payload = verifyToken(token);
        if (payload) {
          req.session!.userId = payload.userId;
          req.session!.companyId = payload.companyId;
          req.session!.role = payload.role;
        }
      }
      return { req, res, redis, conn };
    },
  });

  const apolloDesktopServer = new ApolloServer({
    schema: await buildSchema({
      resolvers: [
        LogResolver,
        UserResolver,
        RoleResolver,
        PermissionResolver,
        CategoryResolver,
        TypeResolver,
        CompanyResolver,
        FeatureResolver,
        InventoryResolver,
        PharmacyResolver,
        ExpenseResolver,
        BillResolver,
        PatientResolver,
        DepartmentResolver,
        ClinicResolver,
        ScheduleResolver,
        SyncResolver,
        MessageResolver,
        PaymentResolver,
      ],
      validate: true,
      emitSchemaFile: true,
    }),
    context: ({ req, res }) => {
      // Extract JWT from Authorization header
      const token = req.headers.authorization?.split(" ")[1];
      if (token) {
        const payload = verifyToken(token);
        if (payload) {
          req.session!.userId = payload.userId;
          req.session!.companyId = payload.companyId;
          req.session!.role = payload.role;
        }
      }
      return { req, res, redis, conn };
    },
  });

  apolloWebServer.applyMiddleware({ app: webApp, cors: false });

  apolloDesktopServer.applyMiddleware({ app: desktopApp, cors: false });

  scheduler(); // initiate scheduler for all cron jobs in the scheduler

  const PORT = process.env.PORT || 4000;

  // Create both HTTP and HTTPS servers
  webApp.listen(PORT, () => {
    console.log(`HTTP Server running on http://localhost:${PORT}`);
  });

  https.createServer(httpsOptions, desktopApp).listen(443, () => {
    console.log(`HTTPS Server running on https://localhost`);
  });
};

main();
