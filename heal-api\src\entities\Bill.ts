import { Field, ObjectType } from "type-graphql";
import { Colum<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Inventory } from "./Inventory/Inventory";

@ObjectType()
@Entity()
export class Bill extends AuditBaseEntity {
  @Field()
  @Column({ default: false })
  cleared: boolean; // shows if the bill has been cleared by cash or otherwise

  @Field()
  @Column({ type: "int" })
  inventoryId: number;

  @Field(() => Inventory)
  @OneToOne(() => Inventory, (inventory) => inventory.bill)
  @JoinColumn([
    { name: "inventoryId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  inventoryTransfer: Inventory;

  @Field()
  @Column({ type: "int", default: 0, nullable: false })
  amount: number;

  @Field(() => String)
  @Column({
    type: "enum",
    enum: ["cash", "insurance"],
    default: "cash",
  })
  paymentType: string;
}
