import { Field, ObjectType } from "type-graphql";
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
} from "typeorm";
import { AuditBaseEntity } from "./AuditEntity";
import { Company } from "./Company";
import { Inventory } from "./Inventory/Inventory";
import { User } from "./User";
import { Role } from "./Role";
import { Expense } from "./Expense";
import { Store } from "./Inventory/Store";
import { Approval } from "./Approval";
import { Department } from "./Department";

@ObjectType()
@Entity()
export class Employee extends AuditBaseEntity {
  @Field(() => String)
  @Column({ type: "text", default: "NEW" })
  status: string;

  @Field()
  @Column({ type: "int", nullable: true })
  roleId: number;

  @Field(() => Role)
  @ManyToOne(() => Role, (role) => role.employees)
  @JoinColumn([
    { name: "roleId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  role: Role;

  @Field(() => String)
  @Column({ type: "text" })
  designation: string;

  @Field(() => String)
  @Column({ type: "text" })
  licenceNumber: string;

  @Field(() => String)
  @Column({ type: "text", nullable: true })
  image: string;

  @Field()
  @Column({ type: "int" })
  userId: number;

  @Field(() => User, { nullable: false })
  @OneToOne(() => User, (user) => user.employee)
  @JoinColumn([
    { name: "userId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  user: User;

  @Field(() => Company)
  @ManyToOne(() => Company, (company) => company.employees)
  @JoinColumn({ name: "companyId" })
  company: Company;

  @Field({ nullable: true })
  @Column({ type: "int", nullable: true })
  storeId: number;

  @Field(() => Store, { nullable: true })
  @ManyToOne(() => Store, (store) => store.storeKeepers)
  @JoinColumn([
    { name: "storeId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  store: Store;

  @Field({ nullable: true })
  @Column({ type: "int", nullable: true })
  departmentId: number;

  @Field(() => Department, { nullable: true })
  @ManyToOne(() => Department, (department) => department.employees)
  @JoinColumn([
    { name: "departmentId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  department: Department;

  @Field(() => [Expense], { nullable: true })
  @OneToMany(() => Expense, (expense) => expense.authorizer)
  authorizedExpenses: Expense[];

  @Field(() => [Expense], { nullable: true })
  @OneToMany(() => Expense, (expense) => expense.requester)
  requestedExpenses: Expense[];

  @Field({ nullable: true })
  @Column({ type: "int", nullable: true })
  headingDepartmentId: number;

  @Field(() => Department, { nullable: true })
  @OneToOne(() => Department, (department) => department.headOfDepartment)
  @JoinColumn([
    { name: "headingDepartmentId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  headingDepartment: Department;

  @Field(() => [Inventory], { nullable: true })
  @OneToMany(() => Inventory, (inventory) => inventory.keeper)
  served_stock: Inventory[]; //history of employee granting stock from stock transfer request

  @Field(() => [Inventory], { nullable: true })
  @OneToMany(() => Inventory, (inventory) => inventory.consumer)
  received_stock: Inventory[]; //history of employee requesting and receiving stock

  @Field(() => [Inventory], { nullable: true })
  @OneToMany(() => Inventory, (inventory) => inventory.approver)
  approved_stock: Inventory[]; //history of employee requesting and receiving stock

  @Field(() => [Approval], { nullable: true })
  @OneToMany(() => Approval, (approval) => approval.requester)
  requestedApprovals: Approval[];

  @Field(() => [Approval], { nullable: true })
  @OneToMany(() => Approval, (approval) => approval.approver)
  approvedApprovals: Approval[];
}
