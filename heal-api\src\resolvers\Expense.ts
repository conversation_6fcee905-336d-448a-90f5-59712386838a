import { isAuth } from "../middleware/isAuth";
import { MyContext } from "../types";
import {
  Arg,
  Mutation,
  Resolver,
  Ctx,
  UseMiddleware,
  Field,
  InputType,
  Query,
  Float,
} from "type-graphql";
import { Expense } from "../entities/Expense";
import { BooleanResponse } from "./user";
import { Between } from "typeorm";
import { logError } from "../utils/utils";
import { User } from "../entities/User";

@InputType()
class ExpenseInput {
  @Field()
  expenseDate: string;
  @Field()
  title: string;
  @Field()
  details: string;
  @Field({ nullable: true })
  assetId: number;
  @Field({ nullable: true })
  assetType: "office" | "employee" | "other";
  @Field()
  type: "credit" | "debit";
  @Field(() => Float)
  amount: number;
}

@InputType()
class ExpenseFilterInput {
  @Field({ nullable: true })
  startDate: string;
  @Field({ nullable: true })
  endDate: string;
}

@Resolver(Expense)
export class ExpenseResolver {
  @Query(() => [Expense])
  @UseMiddleware(isAuth)
  async expenses(
    @Arg("filter", { nullable: true }) filter: ExpenseFilterInput,
    @Ctx() { req }: MyContext
  ): Promise<Expense[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const whereClause: any = {
        companyId: req.session.companyId,
        deleted: false,
      };

      if (filter?.startDate && filter?.endDate) {
        const startDate = new Date(filter.startDate);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(filter.endDate);
        endDate.setHours(23, 59, 59, 999);

        whereClause.expenseDate = Between(startDate, endDate);
      }

      return await Expense.find({
        where: whereClause,
        relations: ["authorizer", "requester"],
        order: { expenseDate: "DESC" },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_FETCH_ERROR",
        JSON.stringify(err),
        "medium",
        `Failed to fetch expenses with filter: ${JSON.stringify(filter)}`,
        userId
      );
      return [];
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addExpense(
    @Arg("args", () => ExpenseInput) args: ExpenseInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // check if user exists
      const user = await User.findOne({
        where: { id: userId, companyId },
        relations: ["employee"],
      });
      if (!user) {
        logError(
          companyId,
          "User not found",
          "EXPENSE_ADD_USER_NOT_FOUND",
          JSON.stringify(args),
          "medium",
          `Add expense failed - user not found`,
          userId
        );
        return {
          status: false,
          error: {
            target: "userId",
            message: "User not found",
          },
        };
      }

      // validate expense amount
      if (args.amount <= 0) {
        logError(
          companyId,
          "Invalid expense amount",
          "EXPENSE_ADD_INVALID_AMOUNT",
          JSON.stringify(args),
          "medium",
          `Add expense failed - invalid amount: ${args.amount}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "amount",
            message: "Expense amount must be greater than zero",
          },
        };
      }

      await Expense.create({
        ...args,
        companyId: req.session.companyId,
        requesterId: user.employee?.id,
        expenseDate: args.expenseDate ? new Date(args.expenseDate) : new Date(),
        status: "requested",
        deleted: false,
      }).save();

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_ADD_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to add expense: ${args.title}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editExpense(
    @Arg("id") id: number,
    @Arg("args", () => ExpenseInput) args: ExpenseInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (args.amount === 0) {
      logError(
        companyId,
        "Zero amount expense",
        "EXPENSE_EDIT_ZERO_AMOUNT",
        JSON.stringify({ id, ...args }),
        "medium",
        `Edit expense failed - zero amount: ${id}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "expense amount can not be zero!",
        },
      };
    }

    try {
      const expense = await Expense.findOne({
        where: {
          id,
          companyId: req.session.companyId,
          deleted: false,
        },
      });

      if (!expense) {
        logError(
          companyId,
          "Expense not found",
          "EXPENSE_EDIT_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Edit expense failed - expense not found: ${id}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "Expense does not exist or access denied!",
          },
        };
      }

      await Expense.update(
        { id, companyId: req.session.companyId },
        { ...args, status: "requested" }
      );

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_EDIT_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to edit expense: ${id}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async authorizeExpense(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // Find the expense to authorize
      const expense = await Expense.findOne({
        where: {
          id,
          companyId: req.session.companyId,
          status: "requested", // Only allow authorizing requested expenses
          deleted: false,
        },
      });

      if (!expense) {
        logError(
          companyId,
          "Expense not found or already processed",
          "EXPENSE_AUTHORIZE_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Authorize expense failed - expense not found or not requestable: ${id}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "Expense not found or already processed",
          },
        };
      }

      // Check if the current user is the authorizer
      if (expense.authorizerId !== userId) {
        logError(
          companyId,
          "Unauthorized authorizer",
          "EXPENSE_AUTHORIZE_UNAUTHORIZED",
          JSON.stringify({
            expenseId: id,
            attemptedUserId: userId,
            actualAuthorizerId: expense.authorizerId,
          }),
          "high",
          `Authorize expense failed - unauthorized user ${userId} attempted to authorize expense: ${id}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "You are not authorized to approve this expense",
          },
        };
      }

      // Update the expense status to approved
      await Expense.update(
        { id, companyId: req.session.companyId },
        {
          status: "approved",
          authorizerId: userId,
        }
      );

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_AUTHORIZE_ERROR",
        JSON.stringify({
          error: err,
          expenseId: id,
          userId: userId,
          companyId: companyId,
        }),
        "high",
        `Failed to authorize expense: ${id}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteExpense(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const expense = await Expense.findOne({
        where: {
          id,
          companyId,
          deleted: false,
        },
      });

      if (!expense) {
        logError(
          companyId,
          "Expense not found",
          "EXPENSE_DELETE_NOT_FOUND",
          JSON.stringify({ id }),
          "medium",
          `Delete expense failed - expense not found: ${id}`,
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Expense does not exist!" },
        };
      }

      // Soft delete the expense
      expense.deleted = true;
      await expense.save();

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_DELETE_ERROR",
        JSON.stringify({ error: err, expenseId: id }),
        "high",
        `Failed to delete expense: ${id}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Query(() => [Expense])
  @UseMiddleware(isAuth)
  async getExpenses(@Ctx() { req }: MyContext): Promise<Expense[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Expense.find({
        where: { companyId: req.session.companyId, deleted: false },
        relations: ["authorizer", "requester"],
        order: { expenseDate: "DESC" },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_GET_ALL_ERROR",
        JSON.stringify(err),
        "medium",
        "Failed to fetch all expenses",
        userId
      );
      return [];
    }
  }

  @Query(() => Expense, { nullable: true })
  @UseMiddleware(isAuth)
  async getExpense(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<Expense | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const expense = await Expense.findOne({
        where: { id, companyId: req.session.companyId, deleted: false },
        relations: ["authorizer", "requester"],
      });

      if (!expense) {
        logError(
          companyId,
          "Expense not found",
          "EXPENSE_GET_NOT_FOUND",
          JSON.stringify({ id }),
          "low",
          `Get expense failed - expense not found: ${id}`,
          userId
        );
      }

      return expense;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EXPENSE_GET_ERROR",
        JSON.stringify(err),
        "medium",
        `Failed to fetch expense: ${id}`,
        userId
      );
      return undefined;
    }
  }
}
