import { Inventory } from "../entities/Inventory/Inventory";
import { Transfer } from "../entities/Inventory/StockTransfer";
import { Bill } from "../entities/Bill";
import { getConnection, getRepository } from "typeorm";
import { ErrorLog } from "../entities/ErrorLogs";
import logger from "./logger";

export async function deleteUnclearedSales() {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const salesToDelete = await getConnection()
      .getRepository(Inventory)
      .createQueryBuilder("inventory")
      .where(
        "inventory.type = :type AND inventory.granted IS false AND inventory.updatedAt < :twentyFourHoursAgo",
        {
          type: "bill",
          twentyFourHoursAgo,
        }
      )
      .getMany();

    for (const sale of salesToDelete) {
      // Delete related transfers
      await getConnection()
        .createQueryBuilder()
        .delete()
        .from(Transfer)
        .where("inventoryId = :inventoryId", { inventoryId: sale.id })
        .execute();

      // Delete related bill
      await getConnection()
        .createQueryBuilder()
        .delete()
        .from(Bill)
        .where("inventoryId = :inventoryId", { inventoryId: sale.id })
        .execute();

      // Delete the sale itself
      await getConnection()
        .createQueryBuilder()
        .delete()
        .from(Inventory)
        .where("id = :id", { id: sale.id })
        .execute();
    }

    console.log(`Deleted ${salesToDelete.length} sales with uncleared bills`);
  } catch (error) {
    console.error("Error deleting sales with uncleared bills:", error);
  }
}

export async function logError(
  companyId: number = 0,
  errorMessage: string,
  errorCode: string,
  errorStackTrace: string,
  severity: "low" | "medium" | "high" | "critical",
  action: string,
  userId: number = 0
): Promise<ErrorLog> {
  // Log to Winston first
  logger.error({
    companyId,
    errorCode,
    errorMessage,
    severity,
    action,
    userId,
    stackTrace: errorStackTrace,
  });

  // Then log to database as before
  const errorLogRepository = getRepository(ErrorLog);

  const errorLog = errorLogRepository.create({
    companyId,
    errorMessage,
    errorCode,
    errorStackTrace,
    severity,
    action,
    userId,
    resolved: false,
  });

  try {
    await errorLogRepository.save(errorLog);
    logger.info("Error logged successfully to database", {
      errorCode,
      companyId,
      userId,
    });
    return errorLog;
  } catch (error) {
    logger.error("Failed to save error log to database", {
      originalError: {
        code: errorCode,
        message: errorMessage,
      },
      saveError: error,
    });
    throw new Error("Error logging failed");
  }
}
