import { Field, Float, ObjectType } from "type-graphql";
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, Unique } from "typeorm";
import { AuditBaseEntity } from "../AuditEntity";
import { Item } from "../Item";

@ObjectType()
@Entity()
@Unique(["companyId", "name", "itemId"])
export class Unit extends AuditBaseEntity {
  // Add barcode field
  @Field(() => String, { nullable: true })
  @Column({ type: "text", nullable: true })
  barcode: string;

  @Field(() => String)
  @Column({ type: "text" })
  name!: string; // Unit name, e.g., "Box", "Packet", "Dozen"

  @Field(() => Float)
  @Column({ type: "float" })
  quantity!: number; // Amount of the original item this unit contains

  @Field(() => Float)
  @Column({ type: "bigint", default: 0 })
  price!: number; // Price for this specific unit

  @Field()
  @Column({ type: "int" })
  itemId!: number;

  @Field(() => Item)
  @ManyToOne(() => Item, (item) => item.units, {
    nullable: false,
    onDelete: "CASCADE",
  })
  @JoinColumn([
    { name: "itemId", referencedColumnName: "id" },
    { name: "companyId", referencedColumnName: "companyId" },
  ])
  item!: Item; // Reference to the original item
}
