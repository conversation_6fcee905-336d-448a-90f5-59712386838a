import {
  Resolver,
  Query,
  Mutation,
  Arg,
  Ctx,
  UseMiddleware,
  registerEnumType,
} from "type-graphql";
import { isAuth } from "../middleware/isAuth";
import { In, <PERSON>Than, <PERSON>Than } from "typeorm";
import { MyContext } from "../types";
import { BooleanResponse } from "./user";
import { Payment } from "../entities/Payment";
import { CreatePaymentInput, UpdatePaymentInput } from "../types/payment";

enum PaymentStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  PENDING = "pending",
  TRIAL = "trial",
}

registerEnumType(PaymentStatus, {
  name: "PaymentStatus",
  description: "Status of a payment",
});

@Resolver(Payment)
export class PaymentResolver {
  @Query(() => [Payment])
  @UseMiddleware(isAuth)
  async payments(@Ctx() { req }: MyContext): Promise<Payment[]> {
    return await Payment.find({
      where: { companyId: req.session!.companyId, deleted: false },
      order: { createdAt: "DESC" },
    });
  }

  @Query(() => Payment, { nullable: true })
  @UseMiddleware(isAuth)
  async getPayment(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<Payment | undefined> {
    return await Payment.findOne({
      where: { id, companyId: req.session!.companyId, deleted: false },
      order: { createdAt: "DESC" },
    });
  }

  @Query(() => Payment, { nullable: true })
  async getActivePayment(
    @Arg("companyId") companyId: number
  ): Promise<Payment | undefined> {
    const now = new Date();

    const activeSub = await Payment.findOne({
      where: {
        companyId: companyId,
        deleted: false,
        status: In([PaymentStatus.ACTIVE, PaymentStatus.TRIAL]),
        startDate: LessThan(now),
        endDate: MoreThan(now),
      },
      order: { endDate: "DESC" },
    });

    return activeSub;
  }

  @Query(() => Payment, { nullable: true })
  @UseMiddleware(isAuth)
  async getLatestPayment(
    @Ctx() { req }: MyContext
  ): Promise<Payment | undefined> {
    return await Payment.findOne({
      where: { companyId: req.session!.companyId, deleted: false },
      order: { createdAt: "DESC" },
    });
  }

  @Mutation(() => Payment)
  @UseMiddleware(isAuth)
  async createPayment(
    @Arg("input") input: CreatePaymentInput,
    @Ctx() { req }: MyContext
  ): Promise<Payment> {
    const payment = Payment.create({
      ...input,
      companyId: req.session.companyId,
    });

    return await payment.save();
  }

  @Mutation(() => Payment, { nullable: true })
  @UseMiddleware(isAuth)
  async updatePayment(
    @Arg("id") id: number,
    @Arg("input") input: UpdatePaymentInput,
    @Ctx() { req }: MyContext
  ): Promise<Payment | null> {
    const payment = await Payment.findOne({
      where: { id, companyId: req.session.companyId },
    });

    if (!payment) return null;

    Object.assign(payment, input);

    return await payment.save();
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deletePayment(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const payment = await Payment.findOne({
      where: { id, companyId: req.session.companyId },
    });

    if (!payment) {
      return {
        status: false,
        error: { target: "payment", message: "Payment not found" },
      };
    }

    payment.deleted = true;
    await payment.save();

    return {
      status: true,
    };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async cancelPayment(
    @Arg("id") id: number,
    @Arg("reason", { nullable: true }) reason: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const payment = await Payment.findOne({
      where: { id, companyId: req.session!.companyId },
    });

    if (!payment) {
      return {
        status: false,
        error: { target: "payment", message: "Payment not found" },
      };
    }

    payment.status = PaymentStatus.CANCELED;
    payment.cancellationReason = reason || "User canceled";
    await payment.save();

    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changePaymentStatus(
    @Arg("id") id: number,
    @Arg("companyId") companyId: number,
    @Arg("status") status: PaymentStatus
  ): Promise<BooleanResponse> {
    const payment = await Payment.findOne({
      where: { id, companyId },
    });

    //query payments from company and check if any are active or trial
    const activePayment = await Payment.findOne({
      where: {
        companyId,
        status: In(["active", "trial"]),
        endDate: MoreThan(new Date()),
        deleted: false,
      },
    });

    if (!payment) {
      return {
        status: false,
        error: { target: "payment", message: "Payment not found" },
      };
    }

    payment.status = status;
    let startDate = new Date(); // Update start date to today
    // Set end date to same date next month
    let endDate = new Date(payment.startDate);
    endDate.setMonth(endDate.getMonth() + 1);
    if (activePayment) {
      // start date is end of active payment
      startDate = activePayment.endDate;
      // end date is 1 month from start date
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1);
    }
    payment.startDate = startDate;
    payment.endDate = endDate;
    await payment.save();

    return { status: true };
  }
}
