import { PowerSyncDatabase } from "@powersync/node";
import { AppSchema } from "./Schema";
import { PowerSyncConnector } from "./PowerSyncConnector";

// Instantiate the PowerSync database with the schema
export const db = new PowerSyncDatabase({
  schema: AppSchema,
  database: {
    dbFilename: "powersync.db",
  },
});

// Connect to the PowerSync service using the connector
export async function connectPowerSync() {
  const connector = new PowerSyncConnector();
  await db.connect(connector);
}

// Automatically start PowerSync when the API starts
connectPowerSync().catch((err) => {
  console.error("Failed to connect PowerSync on startup:", err);
});

// Periodically check sync status and update SyncHistory if fully in sync
setInterval(async () => {
  try {
    // Example: check if local and remote are in sync (customize as needed)
    // This assumes you have a way to compare local and remote sync state
    const local: any = await db.get(
      "SELECT MAX(updatedAt) as lastLocal FROM SyncHistory WHERE status = ?",
      ["success"]
    );
    // Optionally, fetch remote status from PowerSync API if available
    // const remote = ...
    // If in sync, update SyncHistory or log status
    if (local && local.lastLocal) {
      await db.execute(
        "INSERT INTO SyncHistory (entityName, direction, status, lastSyncTimestamp, companyId, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?)",
        [
          "all",
          "up",
          "success",
          local.lastLocal,
          0,
          local.lastLocal,
          local.lastLocal,
        ]
      );
    }
  } catch (err) {
    // Log but do not crash
    console.error("Periodic PowerSync status update error:", err);
  }
}, 5 * 60 * 1000); // Every 5 minutes

// Usage: Call connectPowerSync() on app startup or user login
