import {
  Resolver,
  Mutation,
  Arg,
  Query,
  InputType,
  Field,
  UseMiddleware,
  Int,
  Ctx,
} from "type-graphql";
import { Department } from "../entities/Department";
import { Employee } from "../entities/Employee";
import { getRepository } from "typeorm";
import { BooleanResponse } from "./user";
import { isAuth } from "../middleware/isAuth";
import { MyContext } from "../types";
import { logError } from "../utils/utils";

@InputType()
class DepartmentInputArgs {
  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  type?: string;

  @Field(() => Number, { nullable: true })
  headOfDepartmentId?: number;

  @Field(() => Number, { nullable: true })
  parentId?: number;
}

@Resolver(Department)
export class DepartmentResolver {
  // Mutation to create a department
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addDepartment(
    @Arg("params") params: DepartmentInputArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // Check if the head of department exists
      let headOfDepartment = null;
      if (params.headOfDepartmentId) {
        headOfDepartment = await getRepository(Employee).findOne({
          id: params.headOfDepartmentId,
          companyId: req.session.companyId,
        });
        if (!headOfDepartment) {
          await logError(
            companyId,
            "Head of Department not found",
            "DEPARTMENT_ADD_INVALID_HEAD",
            JSON.stringify({ headOfDepartmentId: params.headOfDepartmentId }),
            "medium",
            `Add department failed - invalid head of department: ${params.headOfDepartmentId}`,
            userId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "Head of Department not found",
            },
          };
        }
      }
      // Check if the head of department exists
      let parent = null;
      let parentDepartment = null;
      let departmentCompanyId = Number(req.session.companyId);

      if (params.parentId && params.parentId > 0) {
        parentDepartment = await getRepository(Department).findOne({
          id: params.parentId,
          companyId: req.session.companyId,
        });
        if (!parentDepartment) {
          await logError(
            companyId,
            "Parent Department not found",
            "DEPARTMENT_ADD_INVALID_PARENT",
            JSON.stringify({ parentId: params.parentId }),
            "medium",
            `Add department failed - invalid parent department: ${params.parentId}`,
            userId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "Parent Department not found",
            },
          };
        }
        parent = parentDepartment.id;
        departmentCompanyId = parentDepartment.companyId;
      }

      await Department.create({
        companyId: departmentCompanyId,
        name: params.name,
        description: params.description,
        status: params.status,
        type: parent && parent > 0 ? parentDepartment?.type : params.type,
        headOfDepartment: headOfDepartment!,
        parentId: parent!,
      }).save();

      return { status: true };
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "DEPARTMENT_ADD_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to add department: ${params.name}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  // Mutation to edit a department
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editDepartment(
    @Arg("id") id: number,
    @Arg("params") params: DepartmentInputArgs,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    const department = await Department.findOne({
      where: { id, companyId: req.session.companyId },
      relations: ["headOfDepartment"],
    });

    if (!department) {
      await logError(
        companyId,
        "Department not found",
        "DEPARTMENT_EDIT_NOT_FOUND",
        JSON.stringify({ departmentId: id }),
        "medium",
        `Edit department failed - department not found: ${id}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Department not found",
        },
      };
    }

    try {
      if (params.headOfDepartmentId) {
        const headOfDepartment = await Employee.findOne({
          id: params.headOfDepartmentId,
          companyId: req.session.companyId,
        });
        if (!headOfDepartment) {
          await logError(
            companyId,
            "Head of department not found",
            "DEPARTMENT_EDIT_INVALID_HEAD",
            JSON.stringify({ headOfDepartmentId: params.headOfDepartmentId }),
            "medium",
            `Edit department failed - invalid head of department: ${params.headOfDepartmentId}`,
            userId
          );
          return {
            status: false,
            error: {
              target: "general",
              message: "Head of department not found",
            },
          };
        }
        department.headOfDepartment = headOfDepartment;
      }

      department.name = params.name;
      department.description = params.description || "";
      department.status = params.status || "";
      department.type = params.type || "";
      await department.save();
      return { status: true };
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "DEPARTMENT_EDIT_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to edit department: ${id}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Error updating department",
        },
      };
    }
  }

  // Query to get all departments with employees and head of department
  @Query(() => [Department])
  @UseMiddleware(isAuth)
  async getDepartments(@Ctx() { req }: MyContext): Promise<Department[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Department.find({
        where: { companyId: req.session.companyId },
        relations: ["headOfDepartment", "employees", "clinics"],
      });
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "DEPARTMENT_GET_ALL_ERROR",
        JSON.stringify(err),
        "medium",
        "Failed to fetch departments",
        userId
      );
      return [];
    }
  }

  // mutation to set department head
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async setHeadOfDepartment(
    @Arg("departmentId", () => Int) departmentId: number,
    @Arg("employeeId", () => Int) employeeId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // Find the department by ID
      const department = await Department.findOne({
        where: { id: departmentId, companyId: req.session.companyId },
        relations: ["headOfDepartment"],
      });
      if (!department) {
        await logError(
          companyId,
          "Department not found",
          "DEPARTMENT_SET_HEAD_NOT_FOUND",
          JSON.stringify({ departmentId }),
          "medium",
          `Set head of department failed - department not found: ${departmentId}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "department",
            message: "Department not found",
          },
        };
      }

      // Find the employee by ID
      const employee = await getRepository(Employee).findOne({
        where: { id: employeeId, companyId: req.session.companyId },
      });
      if (!employee) {
        await logError(
          companyId,
          "Employee not found",
          "DEPARTMENT_SET_HEAD_INVALID_EMPLOYEE",
          JSON.stringify({ employeeId }),
          "medium",
          `Set head of department failed - employee not found: ${employeeId}`,
          userId
        );
        return {
          status: false,
          error: {
            target: "employee",
            message: "Employee not found",
          },
        };
      }

      // Set the employee as the head of the department
      department.headOfDepartment = employee;

      // Save the updated department
      await department.save();

      return { status: true };
    } catch (err) {
      await logError(
        companyId,
        err.message,
        "DEPARTMENT_SET_HEAD_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to set head of department: ${departmentId}`,
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }
}
