# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

type Address {
  city: String!
  companies: [Company!]!
  companyId: Float!
  country: String!
  createdAt: String!
  deleted: Boolean!
  district: String!
  id: Float!
  patients: [Patient!]!
  street: String!
  updatedAt: String!
  users: [User!]!
  ward: String!
  zip: String!
}

type Approval {
  approvalDate: DateTime
  approver: Employee
  approverId: Float
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  feature: String!
  id: Float!
  requestId: Float!
  requester: Employee
  requesterId: Float
  status: Boolean!
  type: String!
  updatedAt: String!
}

type BatchStock {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  expireDate: String!
  id: Float!
  item: Item!
  itemId: Float!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  updatedAt: String!
}

type Bill {
  amount: Float!
  cleared: Boolean!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  inventoryId: Float!
  inventoryTransfer: Inventory!
  paymentType: String!
  updatedAt: String!
}

"""Billing cycle for payment"""
enum BillingCycle {
  ANNUALLY
  MONTHLY
  QUARTERLY
}

type BooleanResponse {
  error: FieldError
  status: Boolean!
}

type BooleanResponseId {
  error: FieldError
  id: Float
  name: String
  status: Boolean!
}

type BooleanResponseWithType {
  data: Type
  error: FieldError
  status: Boolean!
}

input BulkItemInput {
  items: [ItemInput!]!
}

type BulkScheduleResponse {
  error: FieldError
  schedules: [Schedule!]
  status: Boolean!
}

type Category {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  parentCategoryId: Float
  type: Type!
  typeId: Float!
  updatedAt: String!
  user: [User!]
}

input CategoryArgs {
  name: String!
  type: Float!
}

type CategoryResponse {
  category: Category
  error: FieldError
}

input CategoryTypeArgs {
  name: String!
  typeName: String!
}

type Clinic {
  clinicType: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  department: Department
  departmentId: Float!
  description: String!
  id: Float!
  leader: Employee
  leaderId: Float
  name: String!
  schedules: [Schedule!]
  size: Float!
  status: String!
  updatedAt: String!
  visitsToClinic: [VisitToClinic!]
}

input ClinicEditArgs {
  clinicType: String!
  description: String!
  leaderId: Int
  name: String!
  size: Int!
  status: String!
}

input ClinicInputArgs {
  clinicType: String!
  departmentId: Int!
  description: String!
  leaderId: Int
  name: String!
  size: Int!
  status: String!
}

type ClinicResponse {
  clinic: Clinic
  error: FieldError
  status: Boolean!
}

type Company {
  branches: [Float!]!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  departments: [Department!]!
  email: String!
  employees: [Employee!]!
  features: [Feature!]
  id: Float!
  isBranch: Boolean!
  isParent: Boolean!
  location: String!
  logo: String!
  name: String!
  parentId: Float!
  payments: [Payment!]
  phone: String!
  poBox: String!
  registrationNumber: String!
  syncHistory: [SyncHistory!]
  syncUrl: String
  tinNumber: String!
  type: String!
  updatedAt: String!
  users: [User!]
  website: String!
}

input CreatePaymentInput {
  amount: Float!
  autoRenew: Boolean
  billingCycle: String!
  endDate: DateTime!
  features: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: DateTime!
  status: String!
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

"""The days of the week"""
enum DayOfWeek {
  FRIDAY
  MONDAY
  SATURDAY
  SUNDAY
  THURSDAY
  TUESDAY
  WEDNESDAY
}

type Department {
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  employees: [Employee!]
  headOfDepartment: Employee
  id: Float!
  name: String!
  parentId: Float
  status: String
  type: String
  updatedAt: String!
}

input DepartmentInputArgs {
  description: String
  headOfDepartmentId: Float
  name: String!
  parentId: Float
  status: String
  type: String
}

input DispatchInput {
  batch: String
  itemId: Float!
  locationId: Float!
  quantity: Float!
  remarks: String
  unit: String!
}

input EditUserArgs {
  email: String!
  firstname: String!
  image: String!
  lastname: String!
  middlename: String!
  phone: String!
}

input EmailPasswordArgs {
  email: String!
  password: String!
}

type Employee {
  approvedApprovals: [Approval!]
  approved_stock: [Inventory!]
  authorizedExpenses: [Expense!]
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  department: Department
  departmentId: Float
  designation: String!
  headingDepartment: Department
  headingDepartmentId: Float
  id: Float!
  image: String!
  licenceNumber: String!
  received_stock: [Inventory!]
  requestedApprovals: [Approval!]
  requestedExpenses: [Expense!]
  role: Role!
  roleId: Float!
  served_stock: [Inventory!]
  status: String!
  store: Store
  storeId: Float
  updatedAt: String!
  user: User!
  userId: Float!
}

type Expense {
  amount: Float!
  assetId: Float!
  assetType: String!
  authorizer: Employee
  authorizerId: Float!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  details: String!
  expenseDate: String!
  id: Float!
  requester: Employee
  requesterId: Float!
  status: String!
  title: String!
  type: String!
  updatedAt: String!
}

input ExpenseFilterInput {
  endDate: String
  startDate: String
}

input ExpenseInput {
  amount: Float!
  assetId: Float
  assetType: String
  details: String!
  expenseDate: String!
  title: String!
  type: String!
}

type Feature {
  companies: [Company!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  updatedAt: String!
}

type FieldError {
  message: String!
  target: String!
}

type Import {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  expireDate: String!
  id: Float!
  importDate: String!
  importPrice: Float!
  item: Item!
  itemId: Float!
  quantity: Float!
  receipt: String!
  sellingPrice: Float!
  supplier: String!
  unit: String!
  updatedAt: String!
}

input ImportInput {
  barcode: String
  batch: String
  expireDate: String
  importDate: String!
  importPrice: Float!
  itemId: Float
  pieceSellingPrice: Float
  quantity: Float!
  receipt: String
  sellingPrice: Float
  subPieceSellingPrice: Float
  supplier: String!
  unit: String!
}

type Inventory {
  approver: Employee
  approverId: Float
  bill: Bill
  companyId: Float!
  consumer: Employee
  consumerId: Float
  createdAt: String!
  customerTag: String
  deleted: Boolean!
  destinationStore: Store
  destinationStoreId: Float
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: Float!
  items: [Item!]!
  keeper: Employee
  keeperId: Float
  received: Boolean!
  returnDate: String
  sourceStore: Store
  sourceStoreId: Float
  startDate: String
  transferDate: String!
  transfers: [Transfer!]!
  type: String!
  updatedAt: String!
}

type Item {
  barcode: String
  batchStocks: [BatchStock!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: Float!
  image: String!
  imports: [Import!]
  internal: Boolean!
  inventoryTransfers: [Inventory!]
  name: String!
  reference: String!
  reorder: Float!
  sellingPrice: Float!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  transfers: [Transfer!]
  type: String!
  unit: String!
  units: [Unit!]!
  updatedAt: String!
}

input ItemInput {
  barcode: String
  description: String
  image: String
  internal: Boolean
  name: String!
  reference: String
  reorder: Float!
  sellingPrice: Float
  type: String!
  unit: String!
}

type LogEntry {
  action: String
  companyId: Int
  errorCode: String
  level: String!
  message: String!
  severity: String
  stackTrace: String
  timestamp: String!
  userId: Int
}

type Message {
  attended: Boolean!
  createdAt: String!
  id: Float!
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
  updatedAt: String!
}

input MessageInput {
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
}

type MessageResponse {
  errors: [FieldError!]
  message: Message
  status: Boolean!
}

type Mutation {
  addCategory(args: CategoryArgs!): BooleanResponse!
  addCategoryWithTypeName(args: CategoryTypeArgs!): CategoryResponse!
  addClinic(params: ClinicInputArgs!): ClinicResponse!
  addCompanyWithAddress(params: RegisterCompanyAddressedArgs!): BooleanResponse!
  addDepartment(params: DepartmentInputArgs!): BooleanResponse!
  addExpense(args: ExpenseInput!): BooleanResponse!
  addFeature(companyId: Float!, name: String!): BooleanResponse!
  addItem(args: ItemInput!): BooleanResponse!
  addItemsFromExcel(args: BulkItemInput!): BooleanResponse!
  addPermission(name: String!, roleId: Float, userId: Float): PermissionResponse!
  addRole(name: String!): BooleanResponse!
  addSchedules(args: ScheduleBulkArgs!): BulkScheduleResponse!
  addService(args: ServiceInput!): BooleanResponse!
  addStore(args: StoreInput!): BooleanResponse!
  addType(args: TypeArgs!): BooleanResponseWithType!
  addUnit(args: UnitInput!): BooleanResponse!
  addVisit(params: VisitInputArgs!): VisitResponse!
  addVitals(params: VitalsInputArgs!): VisitResponse!
  assignStoreKeeper(storeId: Float!, userId: Float!): BooleanResponse!
  authorizeExpense(id: Float!): BooleanResponse!
  cancelPayment(id: Float!, reason: String): BooleanResponse!
  cancelSchedule(reason: String, scheduleId: Float!): BooleanResponse!
  changeEmployeeRole(companyRole: Float!, departmentId: Float!, designation: String!, employeeId: Float!): BooleanResponse!
  changeEmployeeStatus(employeeId: Float!, status: String!): BooleanResponse!
  changeInventoryApprovalStatus(inventoryId: Float!): BooleanResponse!
  changeInventoryDispatchedStatus(inventoryId: Float!): BooleanResponse!
  changeInventoryReceivedStatus(inventoryId: Float!): BooleanResponse!
  changeInventorySoldStatus(inventoryId: Float!): BooleanResponse!
  changePassword(currentPassword: String!, newPassword: String!): BooleanResponse!
  changePaymentStatus(companyId: Float!, id: Float!, status: String!): BooleanResponse!
  clearBill(saleId: Float!): BooleanResponse!
  clearServedBill(inventoryId: Float!): BooleanResponse!
  createPayment(input: CreatePaymentInput!): Payment!
  deleteBillItem(inventoryId: Float!, transferId: Float!): BooleanResponse!
  deleteCategory(id: Float!): BooleanResponse!
  deleteClinic(id: Int!): BooleanResponse!
  deleteExpense(id: Float!): BooleanResponse!
  deleteFeature(id: Float!): BooleanResponse!
  deleteItem(id: Float!): BooleanResponse!
  deletePayment(id: Float!): BooleanResponse!
  deletePermission(id: Float!): BooleanResponse!
  deleteRole(id: Float!): BooleanResponse!
  deleteSchedule(id: Float!): BooleanResponse!
  deleteType(id: Float!): BooleanResponse!
  deleteUnit(id: Float!): BooleanResponse!
  dispatchItems(args: [DispatchInput!]!): BooleanResponse!
  editBillItem(inventoryId: Float!, newQuantity: Float!, transferId: Float!): TransferResponse!
  editCategory(args: CategoryArgs!, id: Float!): BooleanResponse!
  editCategoryByName(args: CategoryArgs!, name: String!): BooleanResponse!
  editClinic(id: Int!, params: ClinicEditArgs!): ClinicResponse!
  editDepartment(id: Float!, params: DepartmentInputArgs!): BooleanResponse!
  editExpense(args: ExpenseInput!, id: Float!): BooleanResponse!
  editFeature(id: Float!, name: String!): BooleanResponse!
  editItem(args: ItemInput!, id: Float!): BooleanResponse!
  editPatient(id: Float!, params: RegisterPatientArgs!): PatientResponse!
  editPermission(id: Float!, name: String!): BooleanResponse!
  editRole(args: RoleArgs!, id: Float!): BooleanResponse!
  editSchedules(args: ScheduleEditBulkArgs!): BulkScheduleResponse!
  editService(args: ServiceInput!, id: Float!): BooleanResponse!
  editStore(args: StoreEditInput!): BooleanResponse!
  editType(args: TypeEditArgs!, id: Float!): BooleanResponse!
  editUser(id: Float!, params: EditUserArgs!): BooleanResponse!
  forcePowerSync: Boolean!
  forgotPassword(email: String!): BooleanResponse!
  generateBarcode(itemId: Float!): Item
  generateUnitBarcode(unitId: Float!): Unit
  importItem(args: ImportInput!): BooleanResponse!
  instantTransfer(args: [TransferInput!]!, destinationStore: Float!, sourceStore: Float!): BooleanResponse!
  login(params: EmailPasswordArgs!): UserResponse!
  logout: Boolean!
  manageUserPermissions(id: Float!, permissions: [Float!]!): BooleanResponse!
  quickSale(args: [SaleInput!]!): BooleanResponse!
  reassignClinicDoctor(clinicId: Int!, newDoctorId: Int!): BooleanResponse!
  receiveMessage(input: MessageInput!): MessageResponse!
  register(params: RegisterUserArgs!): BooleanResponse!
  registerCompany(params: RegisterCompanyArgs!): BooleanResponseId!
  registerEmployee(params: RegisterEmployeeArgs!): BooleanResponse!
  registerPatient(params: RegisterPatientArgs!): PatientResponse!
  removePermission(name: String!, roleId: Float, userId: Float): BooleanResponse!
  resetPassword(newPassword: String!, token: String!): UserResponse!
  servePayLater(args: [SaleInput!]!, customerTag: String, servedTo: Float): BooleanResponse!
  servePendingOrder(transferId: Float!): BooleanResponse!
  setHeadOfDepartment(departmentId: Int!, employeeId: Int!): BooleanResponse!
  transferItems(args: [DispatchInput!]!): BooleanResponse!
  updateBill(args: [SaleInput!]!, inventoryId: Float!): BooleanResponse!
  updateMessageStatus(attended: Boolean!, messageId: Float!): MessageResponse!
  updatePayment(id: Float!, input: UpdatePaymentInput!): Payment
  updateScheduleStatus(scheduleId: Float!, status: String!): BooleanResponse!
  updateUnit(args: UnitInput!, id: Float!): BooleanResponse!
  writeOffItems(args: [WriteOffInput!]!): BooleanResponse!
}

type Patient {
  address: Address!
  addressId: Float!
  alergies: [String!]
  bills: [Bill!]!
  bloodGroup: String
  companyId: Float!
  createdAt: String!
  dateOfBirth: String!
  deleted: Boolean!
  email: String
  fileNumber: String
  firstname: String!
  gender: String!
  id: Float!
  image: String
  insuranceCardNumber: String
  insuranceId: String
  insuranceProvider: String!
  insuranceSchemeId: String
  insuranceStatus: String!
  insuranceUserId: String!
  lastname: String!
  middlename: String!
  nationalId: String
  nextOfKinName: String!
  nextOfKinPhone: String!
  nextOfKinRelationship: String!
  otherId: String
  phone: String!
  registerer: Employee!
  registererId: Float
  religion: String!
  status: String!
  updatedAt: String!
  visits: [Visit!]!
}

type PatientResponse {
  error: FieldError
  patient: Patient
  status: Boolean!
}

type Payment {
  amount: Float!
  autoRenew: Boolean!
  billingCycle: BillingCycle!
  cancellationReason: String
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  endDate: String!
  features: String
  id: Float!
  lastPaymentDate: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: String!
  status: PaymentStatus!
  updatedAt: String!
}

"""Status of a payment"""
enum PaymentStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PENDING
  TRIAL
}

type Permission {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  roles: [Role!]
  updatedAt: String!
  users: [User!]
}

type PermissionResponse {
  error: FieldError
  permission: Permission
  status: Boolean!
}

type Query {
  expenses(filter: ExpenseFilterInput): [Expense!]!
  getActivePayment(companyId: Float!): Payment
  getAllCategories: [Category!]!
  getAllItems: [Item!]!
  getAllMessages(endDate: DateTime!, startDate: DateTime!): [Message!]!
  getAllServices: [Item!]!
  getBatchStockForStore(itemId: Float, storeId: Float): [BatchStock!]!
  getCategories(type: String!): [Category!]!
  getClinic(id: Int!): Clinic
  getClinics: [Clinic!]!
  getCompanies: [Company!]!
  getCompany(id: Float!): Company
  getCompanyErrors(companyId: Int!, days: Int = 7, endDate: DateTime, severity: String, startDate: DateTime): [LogEntry!]!
  getDepartments: [Department!]!
  getDispatches: [Inventory!]!
  getEmployees: [User!]!
  getExpense(id: Float!): Expense
  getExpenses: [Expense!]!
  getFeatures: [Feature!]!
  getInternalItems: [Item!]!
  getInventoryTransfer(id: Float!): Inventory
  getInventoryTransfers(type: String): [Inventory!]!
  getItem(id: Float!): Item!
  getItemBatchImports(itemId: Float!): [Import!]!
  getItemBatchStocks(itemId: Float!): [BatchStock!]!
  getItemByBarcode(barcode: String!): Item
  getItemStoreStocks(itemId: Float!): [StoreItemStock!]!
  getItemTransfers(itemId: Float!, type: String): [Transfer!]!
  getItemUnits(itemId: Float!): [Unit!]!
  getItems: [Item!]!
  getLatestPayment: Payment
  getLogs(companyId: Int, endDate: DateTime, errorCode: String, level: String, startDate: DateTime, userId: Int): [LogEntry!]!
  getMerchandiseItems: [Item!]!
  getMessages(attended: Boolean): [Message!]!
  getOpenTabs: [Inventory!]!
  getPatient(id: Float!): Patient
  getPatients: [Patient!]!
  getPayment(id: Float!): Payment
  getPermissions: [Permission!]!
  getRole(name: String!): Role
  getRoles(sys: Boolean): [Role!]!
  getSales(date: DateTime): [Inventory!]!
  getSalesPOS: [Inventory!]!
  getSchedules(owner: String!, ownerId: Int!): [Schedule!]!
  getStoreItems(storeId: Float!): [Item!]!
  getStores: [Store!]!
  getTodaysErrors: [LogEntry!]!
  getTodaysImports: [Import!]!
  getTransfers: [Inventory!]!
  getType(id: Float!): Type
  getTypes: [Type!]!
  getUndetailedEmployees(companyId: Float!): [UndetailedUser!]!
  getUnit(id: Float!): Unit
  getUnitByBarcode(barcode: String!): Unit
  getUser(id: Float!): User
  getUsers(roles: [Float!]): [User!]!
  getVisits: [Visit!]!
  getWriteOffsByCompany: [Transfer!]!
  lastPowerSyncStatus: String
  me: User
  payments: [Payment!]!
  powerSyncProgress: SyncProgress!
  schedules(clinicId: Float, endDate: DateTime!, startDate: DateTime!): [Schedule!]!
}

input RegisterCompanyAddressedArgs {
  city: String!
  district: String!
  name: String!
  registrationNumber: String!
  street: String!
  tinNumber: String!
  type: String!
  ward: String!
}

input RegisterCompanyArgs {
  location: String
  name: String!
  registrationNumber: String!
  tinNumber: String!
  type: String!
}

input RegisterEmployeeArgs {
  companyRole: Float
  department: Float
  designation: String!
  email: String!
  firstname: String!
  lastname: String!
  licenseNumber: String!
  middlename: String!
  password: String!
  phone: String!
  store: Float
}

input RegisterPatientArgs {
  DOB: String!
  city: String!
  country: String!
  district: String!
  email: String!
  firstname: String!
  gender: String!
  insuranceProvider: String!
  insuranceStatus: String!
  insuranceUserId: String!
  lastname: String!
  middlename: String!
  nationalId: String!
  nextOfKinName: String!
  nextOfKinPhone: String!
  nextOfKinRelationship: String!
  phone: String!
  religion: String!
  status: String!
  street: String!
  ward: String!
}

input RegisterUserArgs {
  companyId: Float!
  email: String!
  firstname: String!
  lastname: String!
  middlename: String!
  password: String!
  phone: String!
}

type Role {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  employees: [Employee!]
  id: Float!
  name: String!
  permissions: [Permission!]
  sys: Boolean!
  updatedAt: String!
  users: [User!]
}

input RoleArgs {
  name: String!
  permissions: [Float!]
}

input SaleInput {
  batch: String
  hold: Boolean = false
  itemId: Float!
  quantity: Float!
  remarks: String
  unit: String!
}

type Schedule {
  clinic: Clinic
  clinicId: Float
  companyId: Float!
  createdAt: String!
  day: DayOfWeek!
  deleted: Boolean!
  description: String
  employee: Employee
  employeeId: Float
  id: Float!
  offTime: String!
  onTime: String!
  status: Status!
  updatedAt: String!
}

input ScheduleBulkArgs {
  clinicId: Float
  employeeId: Float
  schedules: [ScheduleDetailsArgs!]!
}

input ScheduleDetailsArgs {
  day: String!
  description: String
  offTime: String!
  onTime: String!
}

input ScheduleEditBulkArgs {
  schedules: [ScheduleEditDetailsArgs!]!
}

input ScheduleEditDetailsArgs {
  description: String!
  offTime: String!
  onTime: String!
  scheduleId: Float!
}

input ServiceInput {
  description: String
  name: String!
  reference: String
  sellingPrice: Float!
}

enum Status {
  ATTENDED
  CANCELED
  MISSED
  PENDING
  POSTPONED
}

type Store {
  address: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  primary: Boolean!
  stockIn: [Inventory!]!
  stockOut: [Inventory!]!
  storeItemStocks: [StoreItemStock!]
  storeKeepers: [Employee!]
  updatedAt: String!
}

input StoreEditInput {
  address: String
  companyId: Float
  id: Float!
  name: String
  primary: Boolean
}

input StoreInput {
  address: String!
  name: String!
  primary: Boolean = false
}

type StoreItemStock {
  batchId: Float!
  batchStock: BatchStock
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  item: Item!
  itemId: Float!
  stock: Float!
  store: Store!
  storeId: Float!
  updatedAt: String!
}

type SyncError {
  code: String
  details: String
  message: String!
  stack: String
}

type SyncHistory {
  company: Company!
  companyId: Float!
  createdAt: DateTime!
  direction: String!
  entityName: String!
  error: SyncError
  id: Float!
  lastSyncTimestamp: DateTime!
  recordsProcessed: Float!
  status: String!
  updatedAt: DateTime!
}

type SyncProgress {
  lastSyncTimestamp: String
  progress: Int!
  recordsProcessed: Int!
  status: String!
  totalRecords: Int
}

type Transfer {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: Float!
  inventoryId: Float!
  inventoryTransfer: Inventory!
  item: Item!
  itemId: Float!
  price: Float!
  quantity: Float!
  received: Boolean!
  updatedAt: String!
}

input TransferInput {
  batch: String
  itemId: Float!
  quantity: Float!
  unit: String!
}

type TransferResponse {
  error: FieldError
  status: Boolean!
  transfer: Transfer
}

type Type {
  category: [Category!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: Float!
  name: String!
  updatedAt: String!
}

input TypeArgs {
  description: String!
  name: String!
}

input TypeEditArgs {
  categories: [Float!]!
  description: String!
  name: String!
}

type UndetailedUser {
  email: String!
  firstname: String!
  image: String
  lastname: String!
}

type Unit {
  barcode: String
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  item: Item!
  itemId: Float!
  name: String!
  price: Float!
  quantity: Float!
  updatedAt: String!
}

input UnitInput {
  barcode: String
  itemId: Float!
  name: String!
  price: Float!
  quantity: Float!
}

input UpdatePaymentInput {
  amount: Float
  autoRenew: Boolean
  billingCycle: String
  cancellationReason: String
  endDate: DateTime
  features: String
  lastPaymentDate: DateTime
  maxUsers: Float
  packageName: String
  paymentReference: String
  startDate: DateTime
  status: String
}

type User {
  address: String!
  company: Company!
  companyId: Float!
  createdAt: String!
  dateOfBirth: String!
  deleted: Boolean!
  email: String
  employee: Employee
  firstname: String!
  gender: String!
  id: Float!
  image: String
  lastname: String!
  middlename: String!
  permissions: [Permission!]
  phone: String!
  role: Role!
  roleId: Float!
  status: [Category!]
  updatedAt: String!
}

type UserResponse {
  error: FieldError
  token: String
  user: User
}

type Visit {
  active: Boolean!
  bills: [Bill!]!
  careTakerName: String
  careTakerPhone: String
  careTakerRelationship: String
  checkInStatus: String!
  checkInTime: DateTime!
  checkInType: String!
  checkOutStatus: String
  checkOutTime: DateTime!
  checkOutType: String
  client: Patient!
  clientId: Float!
  companyId: Float!
  confirmedDx: String
  consultation: String
  createdAt: String!
  currentLocation: String
  deleted: Boolean!
  folioId: String
  folioNumber: String
  id: Float!
  insuranceAuthNumber: String
  insuranceCardNumber: String
  insuranceId: String
  insuranceProvider: String
  insuranceSchemeId: String
  insuranceStatus: String
  insuranceUserId: String
  preliminaryDx: String
  productCode: String
  reason: String!
  referralType: String!
  status: String!
  supportingFile: String
  ticket: String!
  type: String!
  updatedAt: String!
  visitToClinics: [VisitToClinic!]!
  vitals: Vitals
}

input VisitInputArgs {
  careTakerName: String!
  careTakerPhone: String!
  careTakerRelationship: String!
  checkInType: String!
  clientId: Float!
  clinicId: Float!
  doctorId: Float
  insuranceAuthNumber: String
  insuranceCardNumber: String
  insuranceId: String
  insuranceProvider: String
  insuranceSchemeId: String
  insuranceStatus: String
  insuranceUserId: String
  reason: String!
  referralType: String!
  status: String!
  ticket: String!
  type: String!
  uploadedFile: String
}

type VisitResponse {
  error: FieldError
  visit: Visit
}

type VisitToClinic {
  attendingEmployee: Employee!
  checkInTime: DateTime!
  checkOutTime: DateTime!
  clinic: Clinic!
  clinicId: Float!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  employeeId: Float
  id: Float!
  updatedAt: String!
  visit: Visit!
  visitId: Float!
}

type Vitals {
  bloodGlucose: String!
  bodyTemperature: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  diastolicPressure: String!
  height: String!
  id: Float!
  oxygenSaturation: String!
  pulseRate: String!
  respirationRate: String!
  systolicPressure: String!
  updatedAt: String!
  visit: Visit!
  visitId: Float!
  weight: String!
}

input VitalsInputArgs {
  bloodGlucose: Float!
  bodyTemperature: Float!
  diastolicPressure: Float!
  height: Float!
  oxygenSaturation: Float!
  pulseRate: Float!
  respirationRate: Float!
  systolicPressure: Float!
  visitId: Float!
  weight: Float!
}

input WriteOffInput {
  batch: String
  itemId: Float!
  locationId: Float!
  quantity: Float!
  reason: String
  unit: String!
}
