import { isAuth } from "../middleware/isAuth";
import { MyContext } from "../types";
import {
  Arg,
  Mutation,
  Resolver,
  Ctx,
  UseMiddleware,
  Field,
  InputType,
  Float,
  Query,
} from "type-graphql";
import {
  startOfDay,
  endOfDay,
  endOfMonth,
  addMonths,
  startOfMonth,
  subDays,
} from "date-fns"; // Using date-fns for date manipulation
import { BooleanResponse, FieldError } from "./user";
import { Item } from "../entities/Item";
import { Inventory } from "../entities/Inventory/Inventory";
import { Between, getConnection, getRepository, MoreThan, Not } from "typeorm";
import { Import } from "../entities/Inventory/Import";
import { Employee } from "../entities/Employee";
import { User } from "../entities/User";
import { Company } from "../entities/Company";
import { Store } from "../entities/Inventory/Store";
import { Transfer } from "../entities/Inventory/StockTransfer";
import { BatchStock } from "../entities/Inventory/Batch";
import { Approval } from "../entities/Approval";
import { StoreItemStock } from "../entities/Inventory/StoreItemStock";
import { Bill } from "../entities/Bill";
import { Unit } from "../entities/Inventory/Unit";
import { ObjectType } from "type-graphql";
import { logError } from "../utils/utils";

@ObjectType()
export class TransferResponse {
  @Field()
  status: boolean;

  @Field(() => Transfer, { nullable: true })
  transfer?: Transfer;

  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
}

@InputType()
export class SaleInput {
  @Field()
  itemId!: number;

  @Field(() => Float)
  quantity!: number;

  @Field(() => String)
  unit: string;

  @Field({ defaultValue: false })
  hold?: boolean;

  @Field(() => String, { nullable: true })
  batch?: string;

  @Field(() => String, { nullable: true })
  remarks?: string;
}

@InputType()
export class DispatchInput {
  @Field()
  itemId!: number;

  @Field()
  locationId!: number;

  @Field()
  quantity!: number;

  @Field(() => String)
  unit: string;

  @Field(() => String, { nullable: true })
  batch?: string;

  @Field(() => String, { nullable: true })
  remarks?: string;
}

@InputType()
export class TransferInput {
  @Field()
  itemId!: number;

  @Field()
  quantity!: number;

  @Field(() => String)
  unit: string;

  @Field(() => String, { nullable: true })
  batch?: string;
}

@InputType()
class BulkItemInput {
  @Field(() => [ItemInput])
  items: ItemInput[];
}

@InputType()
class ImportInput {
  @Field()
  importDate: string;
  @Field({ nullable: true })
  expireDate: string;
  @Field()
  supplier: string;
  @Field({ nullable: true })
  itemId?: number;
  @Field({ nullable: true })
  barcode?: string;
  @Field()
  quantity: number;
  @Field()
  unit: string;
  @Field(() => Float)
  importPrice: number;
  @Field({ nullable: true })
  receipt: string;
  @Field({ nullable: true })
  batch: string;
  @Field({ nullable: true })
  sellingPrice: number;
  @Field({ nullable: true })
  pieceSellingPrice: number;
  @Field({ nullable: true })
  subPieceSellingPrice: number;
}

@InputType()
class ItemInput {
  @Field()
  name: string;
  @Field({ nullable: true })
  image: string;
  @Field({ nullable: true })
  description: string;
  @Field()
  type: string;
  @Field({ nullable: true })
  reference: string;
  @Field()
  reorder: number;
  @Field({ nullable: true })
  internal: boolean;
  @Field()
  unit: string;
  @Field({ nullable: true })
  sellingPrice: number;
  @Field({ nullable: true })
  barcode?: string;
}

@InputType()
class ServiceInput {
  @Field()
  name: string;
  @Field({ nullable: true })
  description: string;
  @Field({ nullable: true })
  reference: string;
  @Field()
  sellingPrice: number;
}

@InputType()
export class WriteOffInput {
  @Field()
  itemId: number;

  @Field()
  quantity: number;

  @Field()
  unit: string;

  @Field({ nullable: true })
  reason: string;

  @Field()
  locationId: number;

  @Field({ nullable: true })
  batch: string;
}

@InputType()
export class StoreInput {
  @Field(() => String)
  name!: string;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  primary!: boolean;

  @Field(() => String)
  address!: string;
}

@InputType()
export class StoreEditInput {
  @Field()
  id!: number; // ID of the store to be edited

  @Field({ nullable: true })
  companyId?: number;

  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => Boolean, { nullable: true })
  primary?: boolean;

  @Field(() => String, { nullable: true })
  address?: string;
}

export class TransformedBatch {
  @Field()
  batchStockId: number;

  @Field()
  batch: string;

  @Field()
  expireDate: string;

  @Field()
  totalStock: number;

  @Field()
  totalPieceStock: number;

  @Field()
  totalSubPieceStock: number;
}

@InputType()
class UnitInput {
  @Field()
  name: string;

  @Field(() => Float)
  quantity: number;

  @Field(() => Float)
  price: number;

  @Field()
  itemId: number;

  @Field(() => String, { nullable: true })
  barcode?: string;
}

@Resolver(Item)
@Resolver(Inventory)
export class InventoryResolver {
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getItems(@Ctx() { req }: MyContext): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Item.find({
        where: { companyId: req.session.companyId },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_GET_ITEMS_ERROR",
        JSON.stringify(err),
        "medium",
        "Failed to fetch inventory items",
        userId
      );
      return [];
    }
  }

  //ADD ITEMS TO BE ABLE TO HAVE INVENTORY COUNT
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addItem(
    @Arg("args", () => ItemInput) args: ItemInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (
      !args.name ||
      !args.type ||
      !args.unit ||
      (args.barcode && args.barcode.trim() === "")
    ) {
      logError(
        companyId,
        "Required fields missing or barcode is empty",
        "INVENTORY_ADD_ITEM_VALIDATION_ERROR",
        JSON.stringify(args),
        "medium",
        "Failed to add inventory item - missing required fields or empty barcode",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message:
            "Fields can not be empty and barcode cannot be an empty string!",
        },
      };
    }
    try {
      await Item.create({
        ...args,
        companyId: req.session.companyId,
      }).save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_ADD_ITEM_ERROR",
        JSON.stringify({ error: err, item: args }),
        "high",
        "Failed to create inventory item",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return {
      status: true,
    };
  }

  //ADD ITEMS TO BE ABLE TO HAVE INVENTORY COUNT
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addService(
    @Arg("args", () => ServiceInput) args: ServiceInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!args.name || !args.sellingPrice || args.sellingPrice <= 0) {
      logError(
        companyId,
        "Required fields missing or invalid selling price",
        "INVENTORY_ADD_SERVICE_VALIDATION_ERROR",
        JSON.stringify(args),
        "medium",
        "Failed to add service - invalid input",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Some fields can not be empty!",
        },
      };
    }
    try {
      await Item.create({
        ...args,
        unit: "service",
        type: "service",
        companyId: req.session.companyId,
      }).save();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_ADD_SERVICE_ERROR",
        JSON.stringify({ error: err, service: args }),
        "high",
        "Failed to create service item",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return {
      status: true,
    };
  }

  //ADD STORE
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addStore(
    @Arg("args", () => StoreInput) args: StoreInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        if (args.primary) {
          const primaryStoreCount = await transactionalEntityManager.count(
            Store,
            {
              where: { companyId: req.session.companyId, primary: true },
            }
          );

          if (primaryStoreCount > 0) {
            logError(
              companyId,
              "Primary store already exists",
              "INVENTORY_ADD_STORE_VALIDATION_ERROR",
              JSON.stringify(args),
              "medium",
              "Failed to add store - primary store already exists",
              userId
            );
            throw new Error("There can only be one primary store per company.");
          }
        }

        const newStore = Store.create({
          ...args,
          companyId: req.session.companyId,
        });

        await transactionalEntityManager.save(newStore);
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_ADD_STORE_ERROR",
        JSON.stringify({ error: err, store: args }),
        "high",
        "Failed to create store",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }

    return { status: true };
  }

  //write off inventory
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async writeOffItems(
    @Arg("args", () => [WriteOffInput]) args: WriteOffInput[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId: number = req.session.companyId;
    const userId = req.session.userId;

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized write-off attempt",
            "INVENTORY_WRITE_OFF_AUTH_ERROR",
            JSON.stringify({ userId, employee, admin }),
            "high",
            "Unauthorized employee attempted write-off",
            userId
          );
          throw new Error("Employee not found or not authorized.");
        }

        const inventoryTransfer = transactionalEntityManager.create(Inventory, {
          companyId,
          type: "writeOff",
          transferDate: new Date(),
          details: "Write-off",
          keeperId: employee?.id,
        });
        await transactionalEntityManager.save(inventoryTransfer);

        const transferRecords: Transfer[] = [];
        const writeOffItems: Item[] = [];

        for (const writeOff of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: writeOff.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found for write-off",
              "INVENTORY_WRITE_OFF_ITEM_ERROR",
              JSON.stringify({ writeOff }),
              "medium",
              "Write-off attempted for non-existent item",
              userId
            );
            throw new Error(`Item not found.`);
          }

          const units = await transactionalEntityManager.find(Unit, {
            where: { item, companyId },
          });

          // Calculate the write-off quantity
          const unitMatch = units?.find((u) => u.name === writeOff.unit);

          const writeOffQuantity =
            writeOff.unit === item.unit
              ? writeOff.quantity
              : unitMatch?.quantity
              ? unitMatch.quantity * writeOff.quantity
              : undefined;

          if (writeOffQuantity === undefined || writeOffQuantity < 0) {
            logError(
              companyId,
              "Invalid write-off quantity",
              "INVENTORY_WRITE_OFF_QUANTITY_ERROR",
              JSON.stringify({ writeOff, unitMatch, writeOffQuantity }),
              "medium",
              `Invalid quantity calculation for ${item.name}`,
              userId
            );
            throw new Error(
              `Invalid write-off quantity or unit for ${item.name}.`
            );
          }

          const store = await transactionalEntityManager.findOne(Store, {
            where: { id: writeOff.locationId, companyId },
          });

          if (!store) {
            logError(
              companyId,
              "Store not found for write-off",
              "INVENTORY_WRITE_OFF_STORE_ERROR",
              JSON.stringify({ writeOff }),
              "medium",
              "Write-off attempted for non-existent store",
              userId
            );
            throw new Error(`Store not found.`);
          }

          if (writeOff.unit === item.unit && item.stock < writeOffQuantity) {
            logError(
              companyId,
              "Insufficient stock for write-off",
              "INVENTORY_WRITE_OFF_STOCK_ERROR",
              JSON.stringify({ item, writeOffQuantity }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          let batch: BatchStock | undefined;
          if (!writeOff.batch) {
            logError(
              companyId,
              "No batch selected for write-off",
              "INVENTORY_WRITE_OFF_BATCH_ERROR",
              JSON.stringify({ item, writeOff }),
              "medium",
              `No batch selected for ${item.name}`,
              userId
            );
            throw new Error(
              `${item.name} batch is not selected for write off.`
            );
          } else {
            batch = await transactionalEntityManager.findOne(BatchStock, {
              where: {
                itemId: writeOff.itemId,
                batch: writeOff.batch,
                companyId,
              },
            });

            if (!batch || batch.stock < writeOffQuantity) {
              logError(
                companyId,
                "Insufficient batch stock for write-off",
                "INVENTORY_WRITE_OFF_BATCH_STOCK_ERROR",
                JSON.stringify({ batch, writeOffQuantity, writeOff }),
                "medium",
                `Insufficient batch stock for ${item.name}`,
                userId
              );
              throw new Error(
                `Insufficient ${item.name} stock with batch ${writeOff.batch}.`
              );
            }
          }

          let storeBatchStock = await StoreItemStock.findOne({
            where: {
              itemId: item.id,
              storeId: store.id,
              batchId: batch.id,
              companyId,
            },
          });

          if (!storeBatchStock || storeBatchStock.stock < writeOffQuantity) {
            logError(
              companyId,
              "Insufficient store batch stock for write-off",
              "INVENTORY_WRITE_OFF_STORE_STOCK_ERROR",
              JSON.stringify({
                storeBatchStock,
                writeOffQuantity,
                store,
                writeOff,
              }),
              "medium",
              `Insufficient store batch stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${writeOff.batch} in ${store.name}.`
            );
          }

          storeBatchStock.stock = storeBatchStock.stock - writeOffQuantity;
          batch.stock = batch.stock - writeOffQuantity;
          item.stock = item.stock - writeOffQuantity;

          await transactionalEntityManager.save(batch);
          await transactionalEntityManager.save(storeBatchStock);
          await transactionalEntityManager.save(item);

          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            companyId,
            item,
            quantity: writeOffQuantity,
            details: writeOff.reason,
            batch: batch.batch,
          });

          transferRecords.push(transferRecord);
          writeOffItems.push(item);
        }

        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = writeOffItems;
        await transactionalEntityManager.save(inventoryTransfer);

        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_WRITE_OFF_TRANSACTION_ERROR",
        JSON.stringify({ error: err, writeOffItems: args }),
        "high",
        "Write-off transaction failed",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // dispatch items
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async dispatchItems(
    @Arg("args", () => [DispatchInput]) args: DispatchInput[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId: number = req.session.companyId;
    const userId = req.session.userId;

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized dispatch attempt",
            "INVENTORY_DISPATCH_AUTH_ERROR",
            JSON.stringify({ userId, employee, admin }),
            "high",
            "Unauthorized employee attempted dispatch",
            userId
          );
          throw new Error("Employee not found or not authorized.");
        }

        const inventoryTransfer = await transactionalEntityManager
          .create(Inventory, {
            companyId,
            type: "dispatch",
            transferDate: new Date(),
            details: "Dispatch",
            keeperId: employee?.id,
          })
          .save();

        const transferRecords: Transfer[] = [];
        const dispatchItems: Item[] = [];

        const stockManager = await transactionalEntityManager.findOne(
          Employee,
          {
            where: { userId, companyId },
          }
        );

        if (!stockManager && req.session.role !== "admin") {
          logError(
            companyId,
            "Unauthorized stock manager",
            "INVENTORY_DISPATCH_MANAGER_ERROR",
            JSON.stringify({ userId, stockManager }),
            "high",
            "Non-manager attempted dispatch",
            userId
          );
          throw new Error("You are not allowed this action.");
        }

        let origin: Store | undefined;

        // If employee, ensure they are the store manager for the destination store
        if (stockManager?.storeId) {
          origin = await transactionalEntityManager.findOne(Store, {
            id: stockManager.storeId,
            companyId,
          });
        } else if (req.session.role === "admin") {
          origin = await transactionalEntityManager.findOne(Store, {
            companyId,
            primary: true,
          });
        }

        if (!origin?.id) {
          logError(
            companyId,
            "No valid origin store",
            "INVENTORY_DISPATCH_STORE_ERROR",
            JSON.stringify({ stockManager, role: req.session.role }),
            "high",
            "Failed to determine origin store for dispatch",
            userId
          );
          throw new Error("You are not allowed to manage any store stock.");
        }

        const destination = await transactionalEntityManager.findOne(Store, {
          where: { id: args[0].locationId, companyId },
        });

        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "INVENTORY_DISPATCH_DESTINATION_ERROR",
            JSON.stringify({ destinationId: args[0].locationId }),
            "medium",
            "Invalid destination store for dispatch",
            userId
          );
          throw new Error("Destination store not found.");
        }

        if (destination.id === origin.id) {
          logError(
            companyId,
            "Same store dispatch attempt",
            "INVENTORY_DISPATCH_SAME_STORE_ERROR",
            JSON.stringify({ storeId: origin.id }),
            "medium",
            "Attempted to dispatch within same store",
            userId
          );
          throw new Error("You cannot dispatch items inside the same store.");
        }

        inventoryTransfer.sourceStoreId = origin.id;
        inventoryTransfer.destinationStoreId = destination.id;

        await transactionalEntityManager.save(inventoryTransfer);

        for (const dispatch of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: dispatch.itemId, companyId: companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "INVENTORY_DISPATCH_ITEM_ERROR",
              JSON.stringify({ dispatch }),
              "medium",
              "Dispatch attempted for non-existent item",
              userId
            );
            throw new Error("Item not found.");
          }

          const units = await transactionalEntityManager.find(Unit, {
            where: { item, companyId },
          });

          const unitMatch = units?.find((u: Unit) => u.name === dispatch.unit);

          const dispatchQuantity =
            dispatch.unit === item.unit
              ? dispatch.quantity
              : unitMatch?.quantity
              ? unitMatch.quantity * dispatch.quantity
              : undefined;

          if (dispatchQuantity === undefined || dispatchQuantity < 0) {
            logError(
              companyId,
              "Invalid dispatch quantity",
              "INVENTORY_DISPATCH_QUANTITY_ERROR",
              JSON.stringify({ dispatch, unitMatch, dispatchQuantity }),
              "medium",
              `Invalid quantity calculation for ${item.name}`,
              userId
            );
            throw new Error(
              `Invalid quantity to dispatch or invalid unit for ${item.name}.`
            );
          }

          if (dispatch.unit === item.unit && item.stock < dispatchQuantity) {
            logError(
              companyId,
              "Insufficient stock for dispatch",
              "INVENTORY_DISPATCH_STOCK_ERROR",
              JSON.stringify({ item, dispatchQuantity }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          if (!dispatch.batch) {
            logError(
              companyId,
              "No batch selected for dispatch",
              "INVENTORY_DISPATCH_BATCH_ERROR",
              JSON.stringify({ item, dispatch }),
              "medium",
              `No batch selected for ${item.name}`,
              userId
            );
            throw new Error(`No ${item.name} batch is selected for dispatch.`);
          }

          const batch = await transactionalEntityManager.findOne(BatchStock, {
            where: {
              itemId: dispatch.itemId,
              batch: dispatch.batch,
              companyId,
            },
          });

          if (!batch || batch.stock < dispatchQuantity) {
            logError(
              companyId,
              "Insufficient batch stock for dispatch",
              "INVENTORY_DISPATCH_BATCH_STOCK_ERROR",
              JSON.stringify({ batch, dispatchQuantity, dispatch }),
              "medium",
              `Insufficient batch stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${dispatch.batch}.`
            );
          }

          const storeItem = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                companyId,
                itemId: item.id,
                storeId: origin.id,
                batchId: batch.id,
              },
            }
          );

          if (!storeItem) {
            logError(
              companyId,
              "Store stock not found",
              "INVENTORY_DISPATCH_STORE_STOCK_ERROR",
              JSON.stringify({ item, origin, batch }),
              "medium",
              `Store stock not found for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock in ${origin.name}.`
            );
          }

          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            companyId,
            itemId: item.id,
            quantity: dispatchQuantity,
            details: dispatch.remarks,
            batch: batch.batch,
          });

          transferRecords.push(transferRecord);
          dispatchItems.push(item);
        }

        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = dispatchItems;
        await transactionalEntityManager.save(inventoryTransfer);

        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );

        const approvalRequest = transactionalEntityManager.create(Approval, {
          feature: "INVENTORY",
          type: "TRANSFER",
          companyId,
          status: false,
          requester: employee,
          requestId: inventoryTransfer.id,
        });

        await transactionalEntityManager.save(approvalRequest);
      });

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_DISPATCH_TRANSACTION_ERROR",
        JSON.stringify({ error: err, dispatchItems: args }),
        "high",
        "Dispatch transaction failed",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // quick sale items from POS without holding on the bill or holding on dispatching dispatching...one action all process completed
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async quickSale(
    @Arg("args", () => [SaleInput]) args: SaleInput[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const company = await transactionalEntityManager.findOne(Company, {
          where: { id: companyId },
          relations: ["features"],
        });

        if (!company) {
          logError(
            companyId,
            "Company not active",
            "QUICK_SALE_COMPANY_ERROR",
            JSON.stringify({ companyId }),
            "high",
            "Quick sale attempted for inactive company",
            userId
          );
          throw new Error("Company not active!");
        }

        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized sale attempt",
            "QUICK_SALE_AUTH_ERROR",
            JSON.stringify({ userId, employee, admin }),
            "high",
            "Unauthorized employee attempted quick sale",
            userId
          );
          throw new Error("Employee not found or not authorized.");
        }

        const inventoryTransfer = await transactionalEntityManager
          .create(Inventory, {
            companyId,
            type: "sale",
            transferDate: new Date(),
            details: "Quick Sale",
            keeperId: employee?.id,
          })
          .save();

        const transferRecords: Transfer[] = [];
        const saleItems: Item[] = [];
        const batchStocks: BatchStock[] = [];
        const storeStocks: StoreItemStock[] = [];

        const stockManager = await transactionalEntityManager.findOne(
          Employee,
          {
            where: { userId, companyId },
          }
        );

        if (!stockManager && req.session.role !== "admin") {
          logError(
            companyId,
            "Unauthorized stock manager",
            "QUICK_SALE_MANAGER_ERROR",
            JSON.stringify({ userId, stockManager }),
            "high",
            "Non-manager attempted quick sale",
            userId
          );
          throw new Error("You are not allowed this action.");
        }

        let origin: Store | undefined;

        // If employee, ensure they are the store manager for the sorce store
        if (stockManager?.storeId) {
          origin = await transactionalEntityManager.findOne(Store, {
            id: stockManager.storeId,
            companyId,
          });
        } else if (req.session.role === "admin") {
          origin = await transactionalEntityManager.findOne(Store, {
            companyId,
            primary: true,
          });
        }

        if (!origin?.id) {
          logError(
            companyId,
            "No valid origin store",
            "QUICK_SALE_STORE_ERROR",
            JSON.stringify({ stockManager, role: req.session.role }),
            "high",
            "Failed to determine origin store for sale",
            userId
          );
          throw new Error("You are not allowed to sell any item.");
        }

        inventoryTransfer.sourceStoreId = origin.id;
        await transactionalEntityManager.save(inventoryTransfer);

        let salePrice: number = 0;

        for (const sale of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: sale.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "QUICK_SALE_ITEM_ERROR",
              JSON.stringify({ sale }),
              "medium",
              "Sale attempted for non-existent item",
              userId
            );
            throw new Error("Item not found.");
          }

          // Calculate the actual quantity in terms of standard units
          let standardQuantity: number;

          if (sale.unit === item.unit) {
            // If using standard unit, no conversion needed
            standardQuantity = sale.quantity;
          } else {
            // Find the custom unit and convert to standard units
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: sale.unit,
              },
            });

            if (!customUnit) {
              logError(
                companyId,
                "Invalid unit",
                "QUICK_SALE_UNIT_ERROR",
                JSON.stringify({ sale, itemUnit: item.unit }),
                "medium",
                `Invalid unit specified for ${item.name}`,
                userId
              );
              throw new Error(`Invalid unit '${sale.unit}' for ${item.name}`);
            }

            // Convert to standard units by multiplying by the unit ratio
            standardQuantity = sale.quantity * customUnit.quantity;
          }

          if (standardQuantity <= 0) {
            logError(
              companyId,
              "Invalid sale quantity",
              "QUICK_SALE_QUANTITY_ERROR",
              JSON.stringify({ sale, standardQuantity }),
              "medium",
              `Invalid quantity calculation for ${item.name}`,
              userId
            );
            throw new Error(`Invalid quantity to sale for ${item.name}.`);
          }

          // Check stock levels using standardQuantity
          if (item.stock < standardQuantity) {
            logError(
              companyId,
              "Insufficient stock for sale",
              "QUICK_SALE_STOCK_ERROR",
              JSON.stringify({ item, standardQuantity }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          let batch: BatchStock | undefined;
          let storeItem: StoreItemStock | undefined;

          if (sale.batch) {
            batch = await transactionalEntityManager.findOne(BatchStock, {
              where: { itemId: sale.itemId, batch: sale.batch, companyId },
            });

            if (batch && batch.stock < standardQuantity) {
              logError(
                companyId,
                "Insufficient batch stock for sale",
                "QUICK_SALE_BATCH_STOCK_ERROR",
                JSON.stringify({ batch, standardQuantity, sale }),
                "medium",
                `Insufficient batch stock for ${item.name}`,
                userId
              );
              throw new Error(
                `Insufficient ${item.name} stock with batch ${sale.batch}.`
              );
            }
          }

          if (batch) {
            storeItem = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  companyId,
                  itemId: item.id,
                  storeId: origin.id,
                  batchId: batch.id,
                },
              }
            );
          } else {
            storeItem = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  companyId,
                  itemId: item.id,
                  storeId: origin.id,
                },
              }
            );
          }

          if (!storeItem || storeItem.stock < standardQuantity) {
            logError(
              companyId,
              "Insufficient store stock",
              "QUICK_SALE_STORE_STOCK_ERROR",
              JSON.stringify({ item, origin, storeItem, standardQuantity }),
              "medium",
              `Insufficient store stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock in ${origin.name}.`
            );
          }

          // Calculate sale price based on unit
          let saleUnitPrice: number;
          if (sale.unit === item.unit) {
            saleUnitPrice = item.sellingPrice;
          } else {
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: sale.unit,
              },
            });
            saleUnitPrice = customUnit!.price;
          }

          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            companyId,
            itemId: item.id,
            quantity: standardQuantity,
            details: sale.remarks,
            batch: batch ? batch.batch : undefined,
            dispatched: true,
            price: saleUnitPrice * sale.quantity,
          });

          transferRecords.push(transferRecord);

          // Update stocks using standardQuantity
          item.stock = item.stock - standardQuantity;
          if (batch) batch.stock = batch.stock - standardQuantity;
          storeItem.stock = storeItem.stock - standardQuantity;

          saleItems.push(item);
          if (batch) batchStocks.push(batch);
          storeStocks.push(storeItem);

          // Calculate total price based on the unit's price and quantity
          salePrice = salePrice + sale.quantity * saleUnitPrice;
        }

        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );

        await Promise.all(
          saleItems.map((saleItem) => transactionalEntityManager.save(saleItem))
        );

        await Promise.all(
          batchStocks.map((batchStock) =>
            transactionalEntityManager.save(batchStock)
          )
        );

        await Promise.all(
          storeStocks.map((storeStock) =>
            transactionalEntityManager.save(storeStock)
          )
        );

        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = saleItems;
        await transactionalEntityManager.save(inventoryTransfer);

        const billRequest = transactionalEntityManager.create(Bill, {
          companyId,
          inventoryId: inventoryTransfer.id,
          amount: salePrice,
          cleared: true,
        });

        await transactionalEntityManager.save(billRequest);
        return { status: true };
      });

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "QUICK_SALE_TRANSACTION_ERROR",
        JSON.stringify({ error: err, saleItems: args }),
        "high",
        "Quick sale transaction failed",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async servePayLater(
    @Arg("args", () => [SaleInput]) args: SaleInput[],
    @Arg("servedTo", { nullable: true }) servedTo: number,
    @Arg("customerTag", { nullable: true }) customerTag: string,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);
    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const company = await transactionalEntityManager.findOne(Company, {
          where: { id: companyId },
          relations: ["features"],
        });

        if (!company) {
          logError(
            companyId,
            "Company not active",
            "SERVE_PAY_LATER_COMPANY_ERROR",
            JSON.stringify({ companyId }),
            "high",
            "Pay later sale attempted for inactive company",
            userId
          );
          throw new Error("Company not active!");
        }

        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized sale attempt",
            "SERVE_PAY_LATER_AUTH_ERROR",
            JSON.stringify({ userId, employee, admin }),
            "high",
            "Unauthorized employee attempted pay later sale",
            userId
          );
          throw new Error("Employee not found or not authorized.");
        }

        // Verify servedTo user belongs to same company if provided
        if (servedTo) {
          const user = await User.findOne({
            where: {
              id: servedTo,
              companyId: companyId,
            },
          });
          if (!user) {
            logError(
              companyId,
              "Invalid served-to user",
              "SERVE_PAY_LATER_USER_ERROR",
              JSON.stringify({ servedTo, companyId }),
              "medium",
              "Pay later sale attempted with invalid user",
              userId
            );
            return {
              status: false,
              error: {
                target: "general",
                message: "Invalid user specified",
              },
            };
          }
        }

        const served = await transactionalEntityManager.findOne(Employee, {
          where: { id: servedTo, companyId },
          relations: ["user"],
        });

        const inventoryTransfer = await transactionalEntityManager
          .create(Inventory, {
            companyId,
            type: "sale",
            transferDate: new Date(),
            details: "Serve items clear bill later",
            keeperId: employee?.id,
            consumer: served,
            customerTag: customerTag,
          })
          .save();

        const transferRecords: Transfer[] = [];
        const saleItems: Item[] = [];
        const batchStocks: BatchStock[] = [];
        const storeStocks: StoreItemStock[] = [];

        const stockManager = await transactionalEntityManager.findOne(
          Employee,
          {
            where: { userId, companyId },
          }
        );

        if (!stockManager && req.session.role !== "admin") {
          logError(
            companyId,
            "Unauthorized stock manager",
            "SERVE_PAY_LATER_MANAGER_ERROR",
            JSON.stringify({ userId, stockManager }),
            "high",
            "Non-manager attempted pay later sale",
            userId
          );
          throw new Error("You are not allowed this action.");
        }

        let origin: Store | undefined;

        // If employee, ensure they are the store manager for the sorce store
        if (stockManager?.storeId) {
          origin = await transactionalEntityManager.findOne(Store, {
            id: stockManager.storeId,
            companyId,
          });
        } else if (req.session.role === "admin") {
          origin = await transactionalEntityManager.findOne(Store, {
            companyId,
            primary: true,
          });
        }

        if (!origin?.id) {
          logError(
            companyId,
            "No valid origin store",
            "SERVE_PAY_LATER_STORE_ERROR",
            JSON.stringify({ stockManager, role: req.session.role }),
            "high",
            "Failed to determine origin store for pay later sale",
            userId
          );
          throw new Error("You are not allowed to sell any item.");
        }

        inventoryTransfer.sourceStoreId = origin.id;
        await transactionalEntityManager.save(inventoryTransfer);

        let salePrice: number = 0;

        for (const sale of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: sale.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "SERVE_PAY_LATER_ITEM_ERROR",
              JSON.stringify({ sale }),
              "medium",
              "Pay later sale attempted for non-existent item",
              userId
            );
            throw new Error("Item not found.");
          }

          // Calculate the actual quantity in terms of standard units
          let standardQuantity: number;

          if (sale.unit === item.unit) {
            // If using standard unit, no conversion needed
            standardQuantity = sale.quantity;
          } else {
            // Find the custom unit and convert to standard units
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: sale.unit,
              },
            });

            if (!customUnit) {
              logError(
                companyId,
                "Invalid unit",
                "SERVE_PAY_LATER_UNIT_ERROR",
                JSON.stringify({ sale, itemUnit: item.unit }),
                "medium",
                `Invalid unit specified for ${item.name}`,
                userId
              );
              throw new Error(`Invalid unit '${sale.unit}' for ${item.name}`);
            }

            // Convert to standard units by multiplying by the unit ratio
            standardQuantity = sale.quantity * customUnit.quantity;
          }

          if (standardQuantity <= 0) {
            logError(
              companyId,
              "Invalid sale quantity",
              "SERVE_PAY_LATER_QUANTITY_ERROR",
              JSON.stringify({ sale, standardQuantity }),
              "medium",
              `Invalid quantity calculation for ${item.name}`,
              userId
            );
            throw new Error(`Invalid quantity to sale for ${item.name}.`);
          }

          // Check stock levels using standardQuantity
          if (!sale.hold && item.stock < standardQuantity) {
            logError(
              companyId,
              "Insufficient stock for sale",
              "SERVE_PAY_LATER_STOCK_ERROR",
              JSON.stringify({ item, standardQuantity }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          let batch: BatchStock | undefined;
          let storeItem: StoreItemStock | undefined;

          if (sale.batch) {
            batch = await transactionalEntityManager.findOne(BatchStock, {
              where: { itemId: sale.itemId, batch: sale.batch, companyId },
            });

            if (batch && !sale.hold && batch.stock < standardQuantity) {
              logError(
                companyId,
                "Insufficient batch stock for sale",
                "SERVE_PAY_LATER_BATCH_STOCK_ERROR",
                JSON.stringify({ batch, standardQuantity, sale }),
                "medium",
                `Insufficient batch stock for ${item.name}`,
                userId
              );
              throw new Error(
                `Insufficient ${item.name} stock with batch ${sale.batch}.`
              );
            }
          }

          if (batch) {
            storeItem = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  companyId,
                  itemId: item.id,
                  storeId: origin.id,
                  batchId: batch.id,
                },
              }
            );
          } else {
            storeItem = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  companyId,
                  itemId: item.id,
                  storeId: origin.id,
                },
              }
            );
          }

          if (!storeItem) {
            logError(
              companyId,
              "Store item not found",
              "SERVE_PAY_LATER_STORE_STOCK_ERROR",
              JSON.stringify({ item, origin }),
              "medium",
              `Store stock not found for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock in ${origin.name}.`
            );
          }

          // Calculate sale price based on unit
          let saleUnitPrice: number;
          if (sale.unit === item.unit) {
            saleUnitPrice = item.sellingPrice;
          } else {
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: sale.unit,
              },
            });
            saleUnitPrice = customUnit!.price;
          }

          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            itemId: item.id,
            companyId,
            quantity: standardQuantity, // Store the standard quantity
            details: sale.remarks,
            batch: batch ? batch.batch : undefined,
            dispatched: sale.hold ? !sale.hold : true,
            price: saleUnitPrice * sale.quantity,
          });

          transferRecords.push(transferRecord);

          if (!sale.hold) {
            item.stock = item.stock - standardQuantity;
            if (batch) batch.stock = batch.stock - standardQuantity;
            storeItem.stock = storeItem.stock - standardQuantity;
          }

          saleItems.push(item);
          if (sale.hold) continue; // if item is on hold and not dispatched do not create a bill for it
          if (batch) batchStocks.push(batch);
          storeStocks.push(storeItem);

          // Calculate total price based on the unit's price and quantity
          salePrice = salePrice + sale.quantity * saleUnitPrice;
        }
        inventoryTransfer.granted = saleItems.length > 0 ? true : false;
        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = saleItems;

        await transactionalEntityManager.save(inventoryTransfer);

        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );

        await Promise.all(
          saleItems.map((saleItem) => transactionalEntityManager.save(saleItem))
        );

        await Promise.all(
          batchStocks.map((batchStock) =>
            transactionalEntityManager.save(batchStock)
          )
        );

        await Promise.all(
          storeStocks.map((storeStock) =>
            transactionalEntityManager.save(storeStock)
          )
        );

        const billRequest = transactionalEntityManager.create(Bill, {
          companyId,
          inventoryId: inventoryTransfer.id,
          amount: salePrice,
          cleared: false,
        });

        await transactionalEntityManager.save(billRequest);
        return { status: true };
      });

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SERVE_PAY_LATER_TRANSACTION_ERROR",
        JSON.stringify({ error: err, saleItems: args }),
        "high",
        "Pay later sale transaction failed",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async updateBill(
    @Arg("inventoryId") inventoryId: number,
    @Arg("args", () => [SaleInput]) args: SaleInput[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);
    try {
      await getConnection().transaction(async (tm) => {
        // 1. Retrieve the existing inventory (bill) to update.
        const inventory = await tm.findOne(Inventory, {
          where: { id: inventoryId, companyId, type: "sale", deleted: false },
          relations: ["transfers", "items", "bill", "sourceStore"],
        });
        if (!inventory) {
          logError(
            companyId,
            "Inventory not found",
            "UPDATE_BILL_INVENTORY_ERROR",
            JSON.stringify({ inventoryId }),
            "high",
            "Failed to find inventory for bill update",
            userId
          );
          throw new Error("Inventory (bill) not found.");
        }

        // 2. Get the current transfers.
        const existingTransfers = inventory.transfers || [];

        // 3 Helper: match transfers by itemId and batch (if provided)
        const findMatchingTransfer = (sale: SaleInput): Transfer | undefined =>
          existingTransfers.find(
            (t) =>
              t.itemId === sale.itemId &&
              (sale.batch ? t.batch === sale.batch : true)
          );

        // 4. Determine transfers to update, add, or delete.
        const transfersToUpdate = args.filter((sale) =>
          findMatchingTransfer(sale)
        );
        const transfersToAdd = args.filter(
          (sale) => !findMatchingTransfer(sale)
        );
        const transfersToDelete = existingTransfers.filter(
          (t) =>
            !args.some(
              (sale) =>
                t.itemId === sale.itemId &&
                (sale.batch ? t.batch === sale.batch : true)
            )
        );

        // 5. Process transfers to update.
        for (const sale of transfersToUpdate) {
          const transfer = findMatchingTransfer(sale);
          if (!transfer) continue;
          const oldQuantity = transfer.quantity;

          const item = await tm.findOne(Item, {
            where: { id: sale.itemId, companyId },
          });
          if (!item) {
            logError(
              companyId,
              "Item not found",
              "UPDATE_BILL_ITEM_ERROR",
              JSON.stringify({ sale }),
              "high",
              "Failed to find item during bill update",
              userId
            );
            throw new Error("Item not found.");
          }

          const diff = sale.quantity - oldQuantity;
          if (diff === 0) continue;

          // Check stock if increasing quantity
          if (diff > 0 && item.stock < diff) {
            logError(
              companyId,
              "Insufficient stock",
              "UPDATE_BILL_STOCK_ERROR",
              JSON.stringify({ item, diff, currentStock: item.stock }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient stock for ${item.name}.`);
          }

          // If transfer has a batch, get the BatchStock.
          let batch: BatchStock | undefined;
          if (transfer.batch) {
            batch = await tm.findOne(BatchStock, {
              where: { itemId: item.id, batch: transfer.batch, companyId },
            });
            if (!batch) {
              logError(
                companyId,
                "Batch not found",
                "UPDATE_BILL_BATCH_ERROR",
                JSON.stringify({ transfer, item }),
                "medium",
                "Failed to find batch during bill update",
                userId
              );
              throw new Error("Batch not found");
            }
            if (diff > 0 && batch.stock < diff) {
              logError(
                companyId,
                "Insufficient batch stock",
                "UPDATE_BILL_BATCH_STOCK_ERROR",
                JSON.stringify({ batch, diff }),
                "medium",
                "Insufficient stock in batch",
                userId
              );
              throw new Error("Insufficient stock in batch");
            }
          }

          // Fetch the StoreItemStock for the item.
          const storeItem = await tm.findOne(StoreItemStock, {
            where: {
              companyId,
              itemId: item.id,
              storeId: inventory.sourceStoreId,
            },
          });
          if (!storeItem) {
            logError(
              companyId,
              "Store item stock not found",
              "UPDATE_BILL_STORE_STOCK_ERROR",
              JSON.stringify({ item, storeId: inventory.sourceStoreId }),
              "high",
              "Failed to find store item stock",
              userId
            );
            throw new Error("Store item stock not found");
          }
          if (diff > 0 && storeItem.stock < diff) {
            logError(
              companyId,
              "Insufficient store stock",
              "UPDATE_BILL_STORE_STOCK_QUANTITY_ERROR",
              JSON.stringify({ storeItem, diff }),
              "medium",
              "Insufficient store stock",
              userId
            );
            throw new Error("Insufficient store stock");
          }

          // Update transfer
          transfer.quantity = sale.quantity; // Store standard quantity
          transfer.details = sale.remarks ?? "";
          transfer.price =
            diff > 0
              ? transfer.price + (diff * item.sellingPrice) / 1
              : sale.quantity * item.sellingPrice;

          // Adjust stock accordingly.
          if (diff > 0) {
            item.stock = item.stock - diff;
            if (batch) batch.stock = batch.stock - diff;
            storeItem.stock = storeItem.stock - diff;
          } else if (diff < 0) {
            const returned = Math.abs(diff);
            item.stock = item.stock + returned;
            if (batch) batch.stock = batch.stock + returned;
            storeItem.stock = storeItem.stock + returned;
          }
          await tm.save(item);
          if (batch) await tm.save(batch);
          await tm.save(storeItem);
          await tm.save(transfer);
        }

        // 6. Process new transfers to add.
        const newTransferRecords: Transfer[] = [];
        const saleItems: Item[] = [];
        const batchStocks: BatchStock[] = [];
        const storeStocks: StoreItemStock[] = [];

        for (const sale of transfersToAdd) {
          const item = await tm.findOne(Item, {
            where: { id: sale.itemId, companyId },
          });
          if (!item) {
            logError(
              companyId,
              "Item not found for new transfer",
              "UPDATE_BILL_NEW_ITEM_ERROR",
              JSON.stringify({ sale }),
              "high",
              "Failed to find item for new transfer",
              userId
            );
            throw new Error("Item not found.");
          }

          // Calculate standard quantity with unit conversion
          let standardQuantity: number;
          let saleUnitPrice: number;

          if (sale.unit === item.unit) {
            standardQuantity = sale.quantity;
            saleUnitPrice = item.sellingPrice;
          } else {
            const customUnit = await tm.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: sale.unit,
              },
            });

            if (!customUnit) {
              logError(
                companyId,
                "Invalid unit",
                "UPDATE_BILL_NEW_UNIT_ERROR",
                JSON.stringify({ sale, itemUnit: item.unit }),
                "medium",
                `Invalid unit specified for ${item.name}`,
                userId
              );
              throw new Error(`Invalid unit '${sale.unit}' for ${item.name}`);
            }

            standardQuantity = sale.quantity * customUnit.quantity;
            saleUnitPrice = customUnit.price;
          }

          if (item.stock < standardQuantity) {
            logError(
              companyId,
              "Insufficient stock for new transfer",
              "UPDATE_BILL_NEW_STOCK_ERROR",
              JSON.stringify({ item, standardQuantity }),
              "medium",
              `Insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient stock for ${item.name}.`);
          }

          let batch: BatchStock | undefined;
          let storeItem: StoreItemStock | undefined;
          if (sale.batch) {
            batch = await tm.findOne(BatchStock, {
              where: { itemId: sale.itemId, batch: sale.batch, companyId },
            });
            if (batch && batch.stock < standardQuantity) {
              logError(
                companyId,
                "Insufficient batch stock for new transfer",
                "UPDATE_BILL_NEW_BATCH_ERROR",
                JSON.stringify({ batch, standardQuantity }),
                "medium",
                `Insufficient batch stock for ${item.name}`,
                userId
              );
              throw new Error(
                `Insufficient ${item.name} stock with batch ${sale.batch}.`
              );
            }
          }

          if (batch) {
            storeItem = await tm.findOne(StoreItemStock, {
              where: {
                companyId,
                itemId: item.id,
                storeId: inventory.sourceStoreId,
                batchId: batch.id,
              },
            });
          } else {
            storeItem = await tm.findOne(StoreItemStock, {
              where: {
                companyId,
                itemId: item.id,
                storeId: inventory.sourceStoreId,
              },
            });
          }

          if (!storeItem) {
            logError(
              companyId,
              "Store item not found for new transfer",
              "UPDATE_BILL_NEW_STORE_ERROR",
              JSON.stringify({ item, storeId: inventory.sourceStoreId }),
              "high",
              `Store stock not found for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock in ${inventory.sourceStore.name}.`
            );
          }

          const newTransfer = tm.create(Transfer, {
            inventoryId: inventory.id,
            itemId: item.id,
            companyId,
            quantity: standardQuantity, // Store standard quantity
            details: sale.remarks,
            batch: sale.batch ? sale.batch : undefined,
            dispatched: sale.hold ? !sale.hold : true,
            price: saleUnitPrice * sale.quantity,
          });
          newTransferRecords.push(newTransfer);
          if (!sale.hold) item.stock = item.stock - standardQuantity;
          saleItems.push(item);
          if (sale.hold) continue;
          if (batch) batch.stock = batch.stock - standardQuantity;
          storeItem.stock = storeItem.stock - standardQuantity;
          if (batch) batchStocks.push(batch);
          storeStocks.push(storeItem);
        }

        if (newTransferRecords.length > 0) {
          await tm.save(newTransferRecords);
        }
        if (saleItems.length > 0) await tm.save(saleItems);
        if (batchStocks.length > 0) await tm.save(batchStocks);
        if (storeStocks.length > 0) await tm.save(storeStocks);

        // 7. Process transfers to delete.
        for (const transfer of transfersToDelete) {
          const item = await tm.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });
          if (!item) {
            logError(
              companyId,
              "Item not found for delete",
              "UPDATE_BILL_DELETE_ITEM_ERROR",
              JSON.stringify({ transfer }),
              "medium",
              "Failed to find item during transfer deletion",
              userId
            );
            continue;
          }
          if (transfer.dispatched) {
            let batch: BatchStock | undefined;
            if (transfer.batch) {
              batch = await tm.findOne(BatchStock, {
                where: { itemId: item.id, batch: transfer.batch, companyId },
              });
            }
            const storeItem = await tm.findOne(StoreItemStock, {
              where: {
                companyId,
                itemId: item.id,
                storeId: inventory.sourceStoreId,
              },
            });
            if (!storeItem) {
              logError(
                companyId,
                "Store item not found for delete",
                "UPDATE_BILL_DELETE_STORE_ERROR",
                JSON.stringify({ item, storeId: inventory.sourceStoreId }),
                "medium",
                "Store item stock not found during deletion",
                userId
              );
              throw new Error("Store item stock not found");
            }
            item.stock = item.stock + transfer.quantity;
            if (batch) batch.stock = batch.stock + transfer.quantity;
            storeItem.stock = storeItem.stock + transfer.quantity;
            await tm.save(item);
            if (batch) await tm.save(batch);
            await tm.save(storeItem);
          }
          await tm.remove(transfer);
        }

        // 8. Update inventory items based on remaining transfers.
        const remainingTransfers = await tm.find(Transfer, {
          where: { inventoryId, companyId },
          relations: ["item"],
        });
        const updatedItems: Item[] = [];
        for (const t of remainingTransfers) {
          if (!updatedItems.some((i) => i.id === t.itemId)) {
            updatedItems.push(t.item);
          }
        }

        // 9. Recalculate bill amount.
        const salePrice = remainingTransfers.reduce(
          (acc, t) => acc + t.price,
          0
        );
        let bill = await tm.findOne(Bill, {
          where: { inventoryId, companyId },
        });
        if (!bill) {
          logError(
            companyId,
            "Bill not found",
            "UPDATE_BILL_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "high",
            "No bill found to update",
            userId
          );
          throw new Error("No bill to update!");
        }
        bill.amount = salePrice;
        await tm.save(bill);

        // 10. If no transfers remain, delete the inventory and bill.
        if (remainingTransfers.length === 0) {
          await tm.remove(bill);
          await tm.remove(inventory);
        } else {
          await tm
            .createQueryBuilder()
            .delete()
            .from("inventory_items")
            .where("inventoryId = :inventoryId AND companyId_1 = :companyId", {
              inventoryId,
              companyId,
            })
            .execute();
          const insertValues = updatedItems.map((item) => ({
            inventoryId,
            itemId: item.id,
            companyId_1: companyId,
            companyId_2: companyId,
          }));

          await tm
            .createQueryBuilder()
            .insert()
            .into("inventory_items")
            .values(insertValues)
            .execute();
        }
      });
      return { status: true };
    } catch (err: any) {
      logError(
        companyId,
        err.message,
        "UPDATE_BILL_TRANSACTION_ERROR",
        JSON.stringify({ error: err, args }),
        "high",
        "Bill update transaction failed",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  @Mutation(() => TransferResponse)
  @UseMiddleware(isAuth)
  async editBillItem(
    @Arg("inventoryId") inventoryId: number,
    @Arg("transferId") transferId: number,
    @Arg("newQuantity") newQuantity: number,
    @Ctx() { req }: MyContext
  ): Promise<TransferResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);
    try {
      const connection = getConnection();
      let updatedTransfer: Transfer;

      await connection.transaction(async (tm) => {
        // Fetch the transfer record with its related item.
        const transfer = await tm.findOne(Transfer, {
          where: { id: transferId, companyId },
          relations: ["item"],
        });
        if (!transfer) {
          logError(
            companyId,
            "Transfer not found",
            "EDIT_BILL_TRANSFER_NOT_FOUND",
            JSON.stringify({ transferId }),
            "medium",
            `Edit bill failed - transfer not found: ${transferId}`,
            userId
          );
          throw new Error("Transfer not found");
        }
        if (transfer.inventoryId !== inventoryId) {
          logError(
            companyId,
            "Inventory mismatch",
            "EDIT_BILL_INVENTORY_MISMATCH",
            JSON.stringify({
              inventoryId,
              transferId,
              transferInventoryId: transfer.inventoryId,
            }),
            "medium",
            `Edit bill failed - inventory mismatch for transfer: ${transferId}`,
            userId
          );
          throw new Error("Inventory mismatch");
        }

        //  fetch the inventory.
        const inventory = await tm.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: ["sourceStore"],
        });
        if (!inventory) {
          logError(
            companyId,
            "Inventory not found",
            "EDIT_BILL_INVENTORY_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            `Edit bill failed - inventory not found: ${inventoryId}`,
            userId
          );
          throw new Error("Inventory not found");
        }

        const oldQuantity = transfer.quantity;
        const diff = newQuantity - oldQuantity;

        // Fetch the associated item.
        const item = await tm.findOne(Item, {
          where: { id: transfer.itemId, companyId },
        });
        if (!item) {
          logError(
            companyId,
            "Item not found",
            "EDIT_BILL_ITEM_NOT_FOUND",
            JSON.stringify({ itemId: transfer.itemId }),
            "medium",
            `Edit bill failed - item not found: ${transfer.itemId}`,
            userId
          );
          throw new Error("Item not found");
        }

        // If the transfer has a batch, get the BatchStock.
        let batch: BatchStock | undefined;
        if (transfer.batch) {
          batch = await tm.findOne(BatchStock, {
            where: { itemId: item.id, batch: transfer.batch, companyId },
          });
          if (!batch) {
            logError(
              companyId,
              "Batch not found",
              "EDIT_BILL_BATCH_NOT_FOUND",
              JSON.stringify({ itemId: item.id, batch: transfer.batch }),
              "medium",
              `Edit bill failed - batch not found for item: ${item.id}`,
              userId
            );
            throw new Error("Batch not found");
          }
          if (diff > 0 && batch.stock < diff) {
            logError(
              companyId,
              "Insufficient batch stock",
              "EDIT_BILL_INSUFFICIENT_BATCH_STOCK",
              JSON.stringify({ batch, diff, newQuantity }),
              "medium",
              `Edit bill failed - insufficient batch stock for item: ${item.id}`,
              userId
            );
            throw new Error("Insufficient stock in batch");
          }
        }

        console.log(inventory.sourceStoreId);
        // Fetch the StoreItemStock for the item.
        const storeItem = await tm.findOne(StoreItemStock, {
          where: {
            companyId,
            itemId: item.id,
            storeId: inventory.sourceStoreId,
          },
        });
        if (!storeItem) {
          logError(
            companyId,
            "Store item stock not found",
            "EDIT_BILL_STORE_STOCK_NOT_FOUND",
            JSON.stringify({
              itemId: item.id,
              storeId: inventory.sourceStoreId,
            }),
            "medium",
            `Edit bill failed - store stock not found for item: ${item.id}`,
            userId
          );
          throw new Error("Store item stock not found");
        }
        if (diff > 0 && storeItem.stock < diff) {
          logError(
            companyId,
            "Insufficient store stock",
            "EDIT_BILL_INSUFFICIENT_STORE_STOCK",
            JSON.stringify({ storeItem, diff, newQuantity }),
            "medium",
            `Edit bill failed - insufficient store stock for item: ${item.id}`,
            userId
          );
          throw new Error("Insufficient store stock");
        }

        // Adjust stocks based on the difference.
        if (diff > 0) {
          // Deduct additional diff from stocks.
          item.stock = item.stock - diff;
          if (batch) batch.stock = batch.stock - diff;
          storeItem.stock = storeItem.stock - diff;
        } else if (diff < 0) {
          // Return items to stock.
          const returned = Math.abs(diff);
          item.stock = item.stock + returned;
          if (batch) batch.stock = batch.stock + returned;
          storeItem.stock = storeItem.stock + returned;
        }
        // Update transfer quantity.
        transfer.quantity = newQuantity;
        transfer.price =
          diff > 0
            ? transfer.price + (diff * item.sellingPrice) / 1
            : newQuantity * item.sellingPrice;

        // Save updated entities.
        await tm.save(item);
        if (batch) await tm.save(batch);
        await tm.save(storeItem);
        updatedTransfer = await tm.save(transfer);

        // Update the bill amount accordingly.
        const bill = await tm.findOne(Bill, {
          where: { inventoryId, companyId },
        });
        if (bill) {
          // Recalculate the total by summing up all transfers' (price * quantity)
          const transfers = await tm.find(Transfer, {
            where: { inventoryId, companyId },
          });
          bill.amount = transfers.reduce((sum, t) => sum + t.price, 0);
          await tm.save(bill);
        }
      });
      return { status: true, transfer: updatedTransfer! };
    } catch (error: any) {
      logError(
        companyId,
        error.message,
        "EDIT_BILL_ERROR",
        JSON.stringify({ error, inventoryId, transferId, newQuantity }),
        "high",
        `Edit bill failed for transfer: ${transferId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: error.message },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteBillItem(
    @Arg("inventoryId") inventoryId: number,
    @Arg("transferId") transferId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);
    try {
      const connection = getConnection();

      await connection.transaction(async (tm) => {
        // 1. Fetch the transfer record with its related item.
        const transfer = await tm.findOne(Transfer, {
          where: { id: transferId, companyId },
          relations: ["item"],
        });
        if (!transfer) {
          logError(
            companyId,
            "Transfer not found",
            "DELETE_BILL_TRANSFER_NOT_FOUND",
            JSON.stringify({ transferId }),
            "medium",
            `Delete bill item failed - transfer not found: ${transferId}`,
            userId
          );
          throw new Error("Transfer not found");
        }
        if (transfer.inventoryId !== inventoryId) {
          logError(
            companyId,
            "Inventory mismatch",
            "DELETE_BILL_INVENTORY_MISMATCH",
            JSON.stringify({ inventoryId, transferId }),
            "medium",
            `Delete bill item failed - inventory mismatch: ${transferId}`,
            userId
          );
          throw new Error("Inventory mismatch");
        }

        // 2. Fetch the associated item.
        const item = await tm.findOne(Item, {
          where: { id: transfer.itemId, companyId },
        });
        if (!item) {
          logError(
            companyId,
            "Item not found",
            "DELETE_BILL_ITEM_NOT_FOUND",
            JSON.stringify({ itemId: transfer.itemId }),
            "medium",
            `Delete bill item failed - item not found: ${transfer.itemId}`,
            userId
          );
          throw new Error("Item not found");
        }

        // 3. Revert stock changes (only if the transfer was dispatched).
        if (transfer.dispatched) {
          item.stock = item.stock + transfer.quantity;
        }

        // 3.1. fetch the inventory.
        const inventory = await tm.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: ["items"],
        });
        if (!inventory) {
          logError(
            companyId,
            "Inventory not found",
            "DELETE_BILL_INVENTORY_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            `Delete bill item failed - inventory not found: ${inventoryId}`,
            userId
          );
          throw new Error("Inventory not found");
        } else if (inventory) {
          // remove this item from the inventory items.
          inventory.items = inventory.items.filter((i) => i.id !== item.id);
          await tm.save(inventory);
        }

        // 4. If transfer has a batch, update the BatchStock.
        let batch: BatchStock | undefined;
        if (transfer.batch) {
          batch = await tm.findOne(BatchStock, {
            where: { itemId: item.id, batch: transfer.batch, companyId },
          });
          if (batch && transfer.dispatched) {
            batch.stock = batch.stock + transfer.quantity;
          }
        }

        // 5. Update the StoreItemStock.
        const storeItem = await tm.findOne(StoreItemStock, {
          where: {
            companyId,
            itemId: item.id,
            storeId: inventory.sourceStoreId,
          },
        });
        if (storeItem && transfer.dispatched) {
          storeItem.stock = storeItem.stock + transfer.quantity;
        }

        // 6. Remove the transfer record.
        await tm.remove(transfer);

        // 7. Check remaining transfers for this inventory.
        const remainingTransfers = await tm.find(Transfer, {
          where: { inventoryId, companyId },
        });

        // 8. Update the Bill amount accordingly.
        const bill = await tm.findOne(Bill, {
          where: { inventoryId, companyId },
        });
        if (remainingTransfers.length === 0) {
          // If no transfers remain, delete the bill and the inventory.
          console.log("no transfers anymore so remove bill and inventory");
          if (bill) {
            await tm.remove(bill);
          }
          const inventory = await tm.findOne(Inventory, {
            where: { id: inventoryId, companyId },
          });
          if (inventory) {
            await tm.remove(inventory);
          }
        } else if (bill) {
          console.log(
            "update the bill amount as we still have: ",
            remainingTransfers
          );
          // Otherwise, update the bill's amount.
          bill.amount = remainingTransfers.reduce((sum, t) => sum + t.price, 0);
          await tm.save(bill);
        }

        // 9. Save updated stocks.
        await tm.save(item);
        if (batch) await tm.save(batch);
        if (storeItem) await tm.save(storeItem);
      });

      return { status: true };
    } catch (error: any) {
      logError(
        companyId,
        error.message,
        "DELETE_BILL_ERROR",
        JSON.stringify({ error, inventoryId, transferId }),
        "high",
        `Delete bill item failed: ${error.message}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: error.message },
      };
    }
  }

  // Create Unit
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addUnit(
    @Arg("args", () => UnitInput) args: UnitInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!args.name || args.quantity <= 0) {
      logError(
        companyId,
        "Invalid unit data",
        "ADD_UNIT_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Required fields missing or invalid quantity",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Name is required and quantity must be greater than 0!",
        },
      };
    }

    // Validate barcode if provided
    if (args.barcode && args.barcode.trim() === "") {
      logError(
        companyId,
        "Empty barcode provided",
        "ADD_UNIT_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Barcode cannot be an empty string",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Barcode cannot be an empty string!",
        },
      };
    }

    try {
      await Unit.create({
        ...args,
        companyId,
      }).save();

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ADD_UNIT_ERROR",
        JSON.stringify({ error: err, unit: args }),
        "high",
        "Failed to create unit",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // Update Unit
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async updateUnit(
    @Arg("id") id: number,
    @Arg("args", () => UnitInput) args: UnitInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!args.name || args.quantity <= 0) {
      logError(
        companyId,
        "Invalid unit update data",
        "UPDATE_UNIT_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Required fields missing or invalid quantity",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Name is required and quantity must be greater than 0!",
        },
      };
    }

    // Validate barcode if provided
    if (args.barcode && args.barcode.trim() === "") {
      logError(
        companyId,
        "Empty barcode provided",
        "UPDATE_UNIT_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Barcode cannot be an empty string",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Barcode cannot be an empty string!",
        },
      };
    }

    try {
      await Unit.update({ id, companyId }, { ...args });
      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "UPDATE_UNIT_ERROR",
        JSON.stringify({ error: err, id, unit: args }),
        "high",
        "Failed to update unit",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // Delete Unit
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteUnit(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const unit = await Unit.findOne({
        where: { id, companyId },
      });

      if (!unit) {
        logError(
          companyId,
          "Unit not found",
          "DELETE_UNIT_ERROR",
          JSON.stringify({ id }),
          "medium",
          "Failed to delete non-existent unit",
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Unit not found!" },
        };
      }

      await Unit.delete({ id, companyId });
      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "DELETE_UNIT_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        "Failed to delete unit",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // Get Units for an Item
  @Query(() => [Unit])
  @UseMiddleware(isAuth)
  async getItemUnits(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext
  ): Promise<Unit[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Unit.find({
        where: { itemId, companyId },
        order: { quantity: "ASC" },
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_UNITS_ERROR",
        JSON.stringify({ error: err, itemId }),
        "high",
        "Failed to fetch item units",
        userId
      );
      return [];
    }
  }

  // Get Single Unit
  @Query(() => Unit, { nullable: true })
  @UseMiddleware(isAuth)
  async getUnit(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<Unit | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Unit.findOne({
        where: { id, companyId },
        relations: ["item"],
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_UNIT_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        "Failed to fetch unit",
        userId
      );
      return undefined;
    }
  }

  // Generate barcode for a unit
  @Mutation(() => Unit, { nullable: true })
  @UseMiddleware(isAuth)
  async generateUnitBarcode(
    @Arg("unitId") unitId: number,
    @Ctx() { req }: MyContext
  ): Promise<Unit | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const unit = await Unit.findOne({
        where: { id: unitId, companyId },
        relations: ["item"],
      });

      if (!unit) {
        logError(
          companyId,
          "Unit not found",
          "GENERATE_UNIT_BARCODE_ERROR",
          JSON.stringify({ unitId }),
          "high",
          "Failed to generate barcode for non-existent unit",
          userId
        );
        throw new Error("Unit not found");
      }

      if (unit.barcode) {
        return unit;
      }

      const barcode = Math.random().toString(36).substring(2, 15);
      unit.barcode = barcode;
      await unit.save();

      return unit;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GENERATE_UNIT_BARCODE_ERROR",
        JSON.stringify({ error: err, unitId }),
        "high",
        "Failed to generate barcode for unit",
        userId
      );
      return undefined;
    }
  }

  // Get Unit by Barcode
  @Query(() => Unit, { nullable: true })
  @UseMiddleware(isAuth)
  async getUnitByBarcode(
    @Arg("barcode") barcode: string,
    @Ctx() { req }: MyContext
  ): Promise<Unit | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Unit.findOne({
        where: { barcode, companyId },
        relations: ["item"],
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_UNIT_BY_BARCODE_ERROR",
        JSON.stringify({ error: err, barcode }),
        "high",
        "Failed to fetch unit by barcode",
        userId
      );
      return undefined;
    }
  }

  //transfer request for items
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async transferItems(
    @Arg("args", () => [DispatchInput]) args: DispatchInput[],
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        // Authorization checks
        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });

        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Employee not found or not authorized",
            "TRANSFER_ITEMS_UNAUTHORIZED",
            JSON.stringify({ userId }),
            "medium",
            "Transfer items failed - unauthorized user",
            userId
          );
          throw new Error("Employee not found or not authorized.");
        }

        // Stock manager validation
        const stockManager = await transactionalEntityManager.findOne(
          Employee,
          {
            where: { userId, companyId },
          }
        );

        if (!stockManager && req.session.role !== "admin") {
          logError(
            companyId,
            "Unauthorized stock management action",
            "TRANSFER_ITEMS_NOT_STOCK_MANAGER",
            JSON.stringify({ userId }),
            "medium",
            "Transfer items failed - user not stock manager",
            userId
          );
          throw new Error("You are not allowed this action.");
        }

        // Store validation
        let destination: Store | undefined;
        if (stockManager && stockManager.storeId) {
          destination = await transactionalEntityManager.findOne(Store, {
            id: stockManager.storeId,
            companyId,
          });
        } else if (req.session.role === "admin") {
          destination = await transactionalEntityManager.findOne(Store, {
            companyId: req.session.companyId,
            primary: true,
          });
        }

        if (!destination) {
          logError(
            companyId,
            "No store management permissions",
            "TRANSFER_ITEMS_NO_STORE_ACCESS",
            JSON.stringify({ userId, stockManagerId: stockManager?.id }),
            "medium",
            "Transfer items failed - no store management access",
            userId
          );
          throw new Error("You are not allowed to manage any store stock.");
        }

        const origin = await transactionalEntityManager.findOne(Store, {
          where: { id: args[0].locationId, companyId },
        });

        if (!origin) {
          logError(
            companyId,
            "Source store not found",
            "TRANSFER_ITEMS_SOURCE_STORE_NOT_FOUND",
            JSON.stringify({ locationId: args[0].locationId }),
            "medium",
            "Transfer items failed - source store not found",
            userId
          );
          throw new Error("Source store not found.");
        }

        if (destination.id === origin.id) {
          logError(
            companyId,
            "Same store transfer attempt",
            "TRANSFER_ITEMS_SAME_STORE",
            JSON.stringify({ storeId: origin.id }),
            "low",
            "Transfer items failed - same store transfer attempt",
            userId
          );
          throw new Error("You cannot transfer items inside the same store.");
        }

        // Create inventory transfer record
        const inventoryTransfer = transactionalEntityManager.create(Inventory, {
          companyId,
          type: "transfer",
          transferDate: new Date(),
          details: "Transfer",
          consumerId: employee?.id,
          sourceStore: origin,
          destinationStore: destination,
        });

        await transactionalEntityManager.save(inventoryTransfer);

        const transferRecords: Transfer[] = [];
        const transferItems: Item[] = [];

        // Validate and process each transfer
        for (const transfer of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "TRANSFER_ITEMS_ITEM_NOT_FOUND",
              JSON.stringify({ itemId: transfer.itemId }),
              "medium",
              "Transfer items failed - item not found",
              userId
            );
            throw new Error("Item not found.");
          }

          // Calculate standard quantity with unit conversion
          let standardQuantity: number;
          if (!transfer.unit || transfer.unit === item.unit) {
            standardQuantity = transfer.quantity;
          } else {
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: transfer.unit,
              },
            });

            if (!customUnit) {
              logError(
                companyId,
                "Invalid unit",
                "TRANSFER_ITEMS_INVALID_UNIT",
                JSON.stringify({
                  itemId: item.id,
                  unit: transfer.unit,
                  itemName: item.name,
                }),
                "medium",
                `Transfer items failed - invalid unit for ${item.name}`,
                userId
              );
              throw new Error(
                `Invalid unit '${transfer.unit}' for ${item.name}.`
              );
            }

            standardQuantity = transfer.quantity * customUnit.quantity;
          }

          if (standardQuantity <= 0) {
            logError(
              companyId,
              "Invalid quantity",
              "TRANSFER_ITEMS_INVALID_QUANTITY",
              JSON.stringify({
                itemId: item.id,
                quantity: standardQuantity,
                itemName: item.name,
              }),
              "medium",
              `Transfer items failed - invalid quantity for ${item.name}`,
              userId
            );
            throw new Error(`Invalid transfer quantity for ${item.name}.`);
          }

          // Batch validation
          if (!transfer.batch) {
            logError(
              companyId,
              "No batch specified",
              "TRANSFER_ITEMS_NO_BATCH",
              JSON.stringify({ itemId: item.id, itemName: item.name }),
              "medium",
              `Transfer items failed - no batch specified for ${item.name}`,
              userId
            );
            throw new Error(`Select batch of ${item.name} for transfer.`);
          }

          const batch = await transactionalEntityManager.findOne(BatchStock, {
            where: {
              itemId: transfer.itemId,
              batch: transfer.batch,
              companyId,
            },
          });

          if (!batch || batch.stock < standardQuantity) {
            logError(
              companyId,
              "Insufficient batch stock",
              "TRANSFER_ITEMS_INSUFFICIENT_BATCH_STOCK",
              JSON.stringify({
                itemId: item.id,
                batch: transfer.batch,
                batchStock: batch?.stock,
                requested: standardQuantity,
                itemName: item.name,
              }),
              "medium",
              `Transfer items failed - insufficient batch stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch}.`
            );
          }

          // Store stock validation
          const storeItem = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                companyId,
                itemId: item.id,
                storeId: origin.id,
                batchId: batch.id,
              },
            }
          );

          if (!storeItem || storeItem.stock < standardQuantity) {
            logError(
              companyId,
              "Store item stock not found",
              "TRANSFER_ITEMS_STORE_STOCK_NOT_FOUND",
              JSON.stringify({
                itemId: item.id,
                storeId: origin.id,
                batch: transfer.batch,
                available: storeItem?.stock,
                requested: standardQuantity,
                itemName: item.name,
              }),
              "medium",
              `Transfer items failed - store stock not found for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch} in ${origin.name}.`
            );
          }

          // Create transfer record
          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            companyId,
            item,
            quantity: standardQuantity, // Store the standard quantity
            details: transfer.remarks,
            batch: batch.batch,
          });

          transferRecords.push(transferRecord);
          transferItems.push(item);
        }

        // Save all records
        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = transferItems;
        await transactionalEntityManager.save(inventoryTransfer);

        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );

        // Create approval request
        const approvalRequest = transactionalEntityManager.create(Approval, {
          feature: "INVENTORY",
          type: "TRANSFER",
          companyId,
          status: false,
          requester: employee,
          requestId: inventoryTransfer.id,
        });

        await transactionalEntityManager.save(approvalRequest);
      });

      return { status: true };
    } catch (err: any) {
      logError(
        companyId,
        err.message,
        "TRANSFER_ITEMS_ERROR",
        JSON.stringify({ error: err, args }),
        "high",
        "Transfer items failed - unexpected error",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // approve inventory transfer
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changeInventoryApprovalStatus(
    @Arg("inventoryId") inventoryId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: ["keeper"],
        });

        if (!inventory) {
          logError(
            companyId,
            "Inventory transfer not found",
            "INVENTORY_APPROVAL_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            "Change inventory approval status failed - inventory not found",
            userId
          );
          throw new Error("Inventory transfer not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized approval attempt",
            "INVENTORY_APPROVAL_UNAUTHORIZED",
            JSON.stringify({
              userId,
              inventoryId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            "Change inventory approval status failed - unauthorized user",
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        // Change the granted (approved) status to true
        inventory.granted = true;
        await transactionalEntityManager.save(inventory);
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_APPROVAL_ERROR",
        JSON.stringify({ error: err, inventoryId }),
        "high",
        "Change inventory approval status failed - unexpected error",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // make dispatch complete on keeper side
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changeInventoryDispatchedStatus(
    @Arg("inventoryId") inventoryId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);
    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: [
            "keeper",
            "consumer",
            "transfers",
            "items",
            "sourceStore",
          ],
        });

        if (!inventory) {
          logError(
            companyId,
            "Inventory transfer not found",
            "INVENTORY_DISPATCH_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            "Change inventory dispatch status failed - inventory not found",
            userId
          );
          throw new Error("Inventory transfer not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized dispatch attempt",
            "INVENTORY_DISPATCH_UNAUTHORIZED",
            JSON.stringify({
              userId,
              inventoryId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            "Change inventory dispatch status failed - unauthorized user",
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        let origin: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!origin) {
          logError(
            companyId,
            "Origin store not found",
            "INVENTORY_DISPATCH_ORIGIN_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            "Change inventory dispatch status failed - origin store not found",
            userId
          );
          throw new Error("Origin store not found.");
        }

        let destination: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "INVENTORY_DISPATCH_DESTINATION_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            "Change inventory dispatch status failed - destination store not found",
            userId
          );
          throw new Error("Destination store not found.");
        }

        for (const transfer of inventory.transfers) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "INVENTORY_DISPATCH_ITEM_NOT_FOUND",
              JSON.stringify({ itemId: transfer.itemId }),
              "medium",
              "Change inventory dispatch status failed - item not found",
              userId
            );
            throw new Error(`Item not found.`);
          }

          if (transfer.quantity > 0 && item.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient stock",
              "INVENTORY_DISPATCH_INSUFFICIENT_STOCK",
              JSON.stringify({
                itemId: item.id,
                stock: item.stock,
                requested: transfer.quantity,
              }),
              "medium",
              `Change inventory dispatch status failed - insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          let batch: BatchStock | undefined;
          batch = await transactionalEntityManager.findOne(BatchStock, {
            where: {
              itemId: transfer.itemId,
              batch: transfer.batch,
              companyId,
            },
          });

          if (!batch || batch.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient batch stock",
              "INVENTORY_DISPATCH_INSUFFICIENT_BATCH_STOCK",
              JSON.stringify({
                itemId: item.id,
                batch: transfer.batch,
                batchStock: batch?.stock,
                requested: transfer.quantity,
              }),
              "medium",
              `Change inventory dispatch status failed - insufficient batch stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch}.`
            );
          }

          const storeItem = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                itemId: item.id,
                storeId: origin.id,
                batchId: batch.id,
                companyId,
              },
            }
          );

          if (!storeItem || storeItem.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient store stock",
              "INVENTORY_DISPATCH_INSUFFICIENT_STORE_STOCK",
              JSON.stringify({
                itemId: item.id,
                storeId: origin.id,
                batch: transfer.batch,
                storeStock: storeItem?.stock,
                requested: transfer.quantity,
              }),
              "medium",
              `Change inventory dispatch status failed - insufficient store stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch} in ${origin.name}.`
            );
          }

          storeItem.stock = storeItem.stock - transfer.quantity;
          // Save stores and their new stock counts
          await transactionalEntityManager.save(storeItem);
        }

        // Change the dispatched status to true
        inventory.dispatched = true;
        await transactionalEntityManager.save(inventory);
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_DISPATCH_ERROR",
        JSON.stringify({ error: err, inventoryId }),
        "high",
        "Change inventory dispatch status failed - unexpected error",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // serve pending order
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async servePendingOrder(
    @Arg("transferId") transferId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);
    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const transfer = await transactionalEntityManager.findOne(Transfer, {
          where: { id: transferId, companyId },
        });

        if (!transfer) {
          logError(
            companyId,
            "Item sale not found",
            "SERVE_PENDING_ORDER_TRANSFER_NOT_FOUND",
            JSON.stringify({ transferId }),
            "medium",
            "Serve pending order failed - transfer not found",
            userId
          );
          throw new Error("Item sale not found, add another sale instead.");
        }

        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: transfer?.inventoryId, companyId },
          relations: [
            "keeper",
            "consumer",
            "transfers",
            "items",
            "sourceStore",
            "bill",
          ],
        });

        if (!inventory) {
          logError(
            companyId,
            "Inventory transfer not found",
            "SERVE_PENDING_ORDER_INVENTORY_NOT_FOUND",
            JSON.stringify({ inventoryId: transfer.inventoryId }),
            "medium",
            "Serve pending order failed - inventory not found",
            userId
          );
          throw new Error("Inventory transfer not found.");
        }

        const bill = await transactionalEntityManager.findOne(Bill, {
          where: { id: inventory.bill.id, companyId },
        });
        if (!bill) {
          logError(
            companyId,
            "Bill not found",
            "SERVE_PENDING_ORDER_BILL_NOT_FOUND",
            JSON.stringify({ billId: inventory.bill.id }),
            "medium",
            "Serve pending order failed - bill not found",
            userId
          );
          throw new Error("This sale bill was not found.");
        }

        const item = await transactionalEntityManager.findOne(Item, {
          where: { id: transfer.itemId, companyId },
        });
        if (!item) {
          logError(
            companyId,
            "Item not found",
            "SERVE_PENDING_ORDER_ITEM_NOT_FOUND",
            JSON.stringify({ itemId: transfer.itemId }),
            "medium",
            "Serve pending order failed - item not found",
            userId
          );
          throw new Error("This sale item was not found.");
        }

        if (transfer.quantity > 0 && item.stock < transfer.quantity) {
          logError(
            companyId,
            "Insufficient stock",
            "SERVE_PENDING_ORDER_INSUFFICIENT_STOCK",
            JSON.stringify({
              itemId: item.id,
              itemName: item.name,
              currentStock: item.stock,
              requestedQuantity: transfer.quantity,
            }),
            "medium",
            `Serve pending order failed - insufficient ${item.name} stock`,
            userId
          );
          throw new Error(`Insufficient ${item.name} stock.`);
        }

        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId;
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized access",
            "SERVE_PENDING_ORDER_UNAUTHORIZED",
            JSON.stringify({
              userId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            "Serve pending order failed - unauthorized user",
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        let origin: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!origin) {
          logError(
            companyId,
            "Origin store not found",
            "SERVE_PENDING_ORDER_ORIGIN_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            "Serve pending order failed - origin store not found",
            userId
          );
          throw new Error("Origin store not found.");
        }

        let destination: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "SERVE_PENDING_ORDER_DESTINATION_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            "Serve pending order failed - destination store not found",
            userId
          );
          throw new Error("Destination store not found.");
        }

        let batch: BatchStock | undefined;
        batch = await transactionalEntityManager.findOne(BatchStock, {
          where: {
            itemId: transfer.itemId,
            batch: transfer.batch,
            companyId,
          },
        });

        if (batch && batch.stock < transfer.quantity) {
          logError(
            companyId,
            "Insufficient batch stock",
            "SERVE_PENDING_ORDER_INSUFFICIENT_BATCH_STOCK",
            JSON.stringify({
              itemId: item.id,
              itemName: item.name,
              batch: transfer.batch,
              batchStock: batch.stock,
              requestedQuantity: transfer.quantity,
            }),
            "medium",
            `Serve pending order failed - insufficient batch stock for ${item.name}`,
            userId
          );
          throw new Error(
            `Insufficient ${item.name} stock with batch ${transfer.batch}.`
          );
        }

        let storeItem: StoreItemStock | undefined;
        if (batch)
          storeItem = await transactionalEntityManager.findOne(StoreItemStock, {
            where: {
              itemId: item.id,
              storeId: origin.id,
              batchId: batch.id,
              companyId,
            },
          });
        else
          storeItem = await transactionalEntityManager.findOne(StoreItemStock, {
            where: {
              itemId: item.id,
              storeId: origin.id,
              companyId,
            },
          });

        if (!storeItem || storeItem.stock < transfer.quantity) {
          logError(
            companyId,
            "Insufficient store stock",
            "SERVE_PENDING_ORDER_INSUFFICIENT_STORE_STOCK",
            JSON.stringify({
              itemId: item.id,
              itemName: item.name,
              storeId: origin.id,
              storeName: origin.name,
              storeStock: storeItem?.stock,
              requestedQuantity: transfer.quantity,
            }),
            "medium",
            `Serve pending order failed - insufficient store stock for ${item.name}`,
            userId
          );
          throw new Error(`Insufficient ${item.name} stock in ${origin.name}.`);
        }

        storeItem.stock = storeItem.stock - transfer.quantity;
        if (batch) batch.stock = batch.stock - transfer.quantity;
        item.stock = item.stock - transfer.quantity;
        bill.amount = bill.amount + transfer.price;
        await transactionalEntityManager.save(storeItem);
        if (batch) await transactionalEntityManager.save(batch);
        await transactionalEntityManager.save(item);
        await transactionalEntityManager.save(bill);
        // Change the dispatched status to true
        transfer.dispatched = true;
        inventory.dispatched = true;
        await transactionalEntityManager.save(transfer);
        await transactionalEntityManager.save(inventory);
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "SERVE_PENDING_ORDER_ERROR",
        JSON.stringify({ error: err, transferId }),
        "high",
        "Serve pending order failed - unexpected error",
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // this is for a bill and clear bill then get served situations
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changeInventorySoldStatus(
    @Arg("inventoryId") inventoryId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);
    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: [
            "keeper",
            "consumer",
            "transfers",
            "items",
            "sourceStore",
          ],
        });

        if (!inventory) {
          logError(
            companyId,
            "Inventory transfer not found",
            "INVENTORY_SOLD_STATUS_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            `Change inventory sold status failed - inventory not found: ${inventoryId}`,
            userId
          );
          throw new Error("Inventory transfer not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized access",
            "INVENTORY_SOLD_STATUS_UNAUTHORIZED",
            JSON.stringify({
              userId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            `Change inventory sold status failed - unauthorized user: ${userId}`,
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        let origin: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!origin) {
          logError(
            companyId,
            "Origin store not found",
            "INVENTORY_SOLD_STATUS_ORIGIN_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            `Change inventory sold status failed - origin store not found: ${inventory.sourceStoreId}`,
            userId
          );
          throw new Error("Origin store not found.");
        }

        let destination: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "INVENTORY_SOLD_STATUS_DESTINATION_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            `Change inventory sold status failed - destination store not found: ${inventory.sourceStoreId}`,
            userId
          );
          throw new Error("Destination store not found.");
        }

        for (const transfer of inventory.transfers) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "INVENTORY_SOLD_STATUS_ITEM_NOT_FOUND",
              JSON.stringify({ itemId: transfer.itemId }),
              "medium",
              `Change inventory sold status failed - item not found: ${transfer.itemId}`,
              userId
            );
            throw new Error(`Item not found.`);
          }

          if (transfer.quantity > 0 && item.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient stock",
              "INVENTORY_SOLD_STATUS_INSUFFICIENT_STOCK",
              JSON.stringify({
                itemId: item.id,
                itemName: item.name,
                currentStock: item.stock,
                requestedQuantity: transfer.quantity,
              }),
              "medium",
              `Change inventory sold status failed - insufficient stock for ${item.name}`,
              userId
            );
            throw new Error(`Insufficient ${item.name} stock.`);
          }

          let batch: BatchStock | undefined;
          batch = await transactionalEntityManager.findOne(BatchStock, {
            where: {
              itemId: transfer.itemId,
              batch: transfer.batch,
              companyId,
            },
          });

          if (!batch || batch.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient batch stock",
              "INVENTORY_SOLD_STATUS_INSUFFICIENT_BATCH_STOCK",
              JSON.stringify({
                itemId: item.id,
                itemName: item.name,
                batch: transfer.batch,
                batchStock: batch?.stock,
                requestedQuantity: transfer.quantity,
              }),
              "medium",
              `Change inventory sold status failed - insufficient batch stock for ${item.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch}.`
            );
          }

          const storeItem = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                itemId: item.id,
                storeId: origin.id,
                batchId: batch.id,
                companyId,
              },
            }
          );

          if (!storeItem || storeItem.stock < transfer.quantity) {
            logError(
              companyId,
              "Insufficient store stock",
              "INVENTORY_SOLD_STATUS_INSUFFICIENT_STORE_STOCK",
              JSON.stringify({
                itemId: item.id,
                itemName: item.name,
                storeId: origin.id,
                storeName: origin.name,
                storeStock: storeItem?.stock,
                requestedQuantity: transfer.quantity,
              }),
              "medium",
              `Change inventory sold status failed - insufficient store stock for ${item.name} in ${origin.name}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock with batch ${transfer.batch} in ${origin.name}.`
            );
          }

          storeItem.stock = storeItem.stock - transfer.quantity;
          batch.stock = batch.stock - transfer.quantity;
          item.stock = item.stock - transfer.quantity;

          // Save stocks and their new stock counts
          await transactionalEntityManager.save(storeItem);
          await transactionalEntityManager.save(batch);
          await transactionalEntityManager.save(item);
        }

        // Change the dispatched status to true
        inventory.dispatched = true;
        await transactionalEntityManager.save(inventory);
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_SOLD_STATUS_ERROR",
        JSON.stringify({ error: err, inventoryId }),
        "high",
        `Change inventory sold status failed - unexpected error`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // change bill clearance status for bill and dipatch then clear bill
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async clearServedBill(
    @Arg("inventoryId") inventoryId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: ["keeper", "bill"],
        });
        if (!inventory) {
          logError(
            companyId,
            "Sale not found",
            "CLEAR_SERVED_BILL_INVENTORY_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            `Clear served bill failed - inventory not found: ${inventoryId}`,
            userId
          );
          throw new Error("This sale was not found.");
        }
        const bill = await transactionalEntityManager.findOne(Bill, {
          where: { id: inventory.bill.id, companyId },
        });
        if (!bill) {
          logError(
            companyId,
            "Bill not found",
            "CLEAR_SERVED_BILL_NOT_FOUND",
            JSON.stringify({ billId: inventory.bill.id }),
            "medium",
            `Clear served bill failed - bill not found: ${inventory.bill.id}`,
            userId
          );
          throw new Error("This sale bill was not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized access",
            "CLEAR_SERVED_BILL_UNAUTHORIZED",
            JSON.stringify({
              userId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            `Clear served bill failed - unauthorized user: ${userId}`,
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        await transactionalEntityManager
          .createQueryBuilder()
          .update(Bill)
          .set({ cleared: true })
          .where("id = :id", { id: bill.id, companyId })
          .execute();
        await transactionalEntityManager
          .createQueryBuilder()
          .update(Inventory)
          .set({ granted: true })
          .where("id = :id", { id: inventory.id, companyId })
          .execute();
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "CLEAR_SERVED_BILL_ERROR",
        JSON.stringify({ error: err, inventoryId }),
        "high",
        `Clear served bill failed - unexpected error`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // make dispatch complete on receiver side
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async changeInventoryReceivedStatus(
    @Arg("inventoryId") inventoryId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const userId = Number(req.session.userId);
    const companyId = Number(req.session.companyId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const inventory = await transactionalEntityManager.findOne(Inventory, {
          where: { id: inventoryId, companyId },
          relations: [
            "keeper",
            "consumer",
            "transfers",
            "items",
            "destinationStore",
          ],
        });

        if (!inventory) {
          logError(
            companyId,
            "Inventory transfer not found",
            "INVENTORY_RECEIVED_STATUS_NOT_FOUND",
            JSON.stringify({ inventoryId }),
            "medium",
            `Change inventory received status failed - inventory not found: ${inventoryId}`,
            userId
          );
          throw new Error("Inventory transfer not found.");
        }

        // Check if the user is authorized to change the dispatched status
        const isAdmin = req.session.role === "admin";
        const isApprover =
          inventory.keeper && inventory.keeper.userId === userId; // changed to proper approver uppon implementing permissions
        if (!isAdmin && !isApprover) {
          logError(
            companyId,
            "Unauthorized access",
            "INVENTORY_RECEIVED_STATUS_UNAUTHORIZED",
            JSON.stringify({
              userId,
              isAdmin,
              keeperId: inventory.keeper?.userId,
            }),
            "medium",
            `Change inventory received status failed - unauthorized user: ${userId}`,
            userId
          );
          throw new Error("Not authorized to change the dispatched status.");
        }

        let origin: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.sourceStoreId,
              companyId,
            },
          });
        if (!origin) {
          logError(
            companyId,
            "Origin store not found",
            "INVENTORY_RECEIVED_STATUS_ORIGIN_NOT_FOUND",
            JSON.stringify({ storeId: inventory.sourceStoreId }),
            "medium",
            `Change inventory received status failed - origin store not found: ${inventory.sourceStoreId}`,
            userId
          );
          throw new Error("Origin store not found.");
        }

        let destination: Store | undefined =
          await transactionalEntityManager.findOne(Store, {
            where: {
              id: inventory.destinationStoreId,
              companyId,
            },
          });
        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "INVENTORY_RECEIVED_STATUS_DESTINATION_NOT_FOUND",
            JSON.stringify({ storeId: inventory.destinationStoreId }),
            "medium",
            `Change inventory received status failed - destination store not found: ${inventory.destinationStoreId}`,
            userId
          );
          throw new Error("Destination store not found.");
        }

        for (const transfer of inventory.transfers) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "INVENTORY_RECEIVED_STATUS_ITEM_NOT_FOUND",
              JSON.stringify({ itemId: transfer.itemId }),
              "medium",
              `Change inventory received status failed - item not found: ${transfer.itemId}`,
              userId
            );
            throw new Error(`Something went wrong with getting the item.`);
          }

          let batch: BatchStock | undefined;
          batch = await transactionalEntityManager.findOne(BatchStock, {
            where: {
              itemId: transfer.itemId,
              batch: transfer.batch,
              companyId,
            },
          });

          if (!batch || batch.stock < transfer.quantity) {
            logError(
              companyId,
              "Invalid batch stock",
              "INVENTORY_RECEIVED_STATUS_INVALID_BATCH",
              JSON.stringify({
                itemId: transfer.itemId,
                batch: transfer.batch,
                batchStock: batch?.stock,
                requestedQuantity: transfer.quantity,
              }),
              "medium",
              `Change inventory received status failed - invalid batch stock for item: ${transfer.itemId}`,
              userId
            );
            throw new Error(`Something went wrong with getting the batch.`);
          }

          // // Increase or create destination store stocks for items of that batch
          let destinationStoreStock = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                itemId: item.id,
                storeId: destination.id,
                batchId: batch.id,
                companyId,
              },
            }
          );

          if (!destinationStoreStock) {
            destinationStoreStock = transactionalEntityManager.create(
              StoreItemStock,
              {
                item,
                companyId,
                batchStock: batch,
                store: inventory.destinationStore,
                stock: transfer.quantity,
              }
            );
          } else {
            destinationStoreStock.stock =
              destinationStoreStock.stock + transfer.quantity;
          }

          // Save stores and their new stock counts
          await transactionalEntityManager.save(destinationStoreStock);
        }

        // Change the received status to true
        inventory.received = true;
        await transactionalEntityManager.save(inventory);
      });

      return {
        status: true,
      };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INVENTORY_RECEIVED_STATUS_ERROR",
        JSON.stringify({ error: err, inventoryId }),
        "high",
        `Change inventory received status failed - unexpected error`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  // instant transfer of products
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async instantTransfer(
    @Arg("args", () => [TransferInput]) args: TransferInput[],
    @Arg("sourceStore") sourceStore: number,
    @Arg("destinationStore") destinationStore: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = Number(req.session.userId);

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        // Check authorization
        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId, companyId },
        });
        const admin = await transactionalEntityManager.findOne(User, {
          where: { id: userId, companyId },
          relations: ["role"],
        });
        if (!employee && admin?.role.name !== "admin") {
          logError(
            companyId,
            "Unauthorized access",
            "INSTANT_TRANSFER_UNAUTHORIZED",
            JSON.stringify({
              userId,
              employeeExists: !!employee,
              adminRole: admin?.role.name,
            }),
            "medium",
            `Instant transfer failed - unauthorized user: ${userId}`,
            userId
          );
          throw new Error("Not authorized to perform transfer.");
        }

        // Restrict employees to their managed store (optional enhancement)
        // if (employee && employee.storeId && ![sourceStore, destinationStore].includes(employee.storeId)) {
        //   throw new Error("Employee can only transfer to/from their managed store.");
        // }

        // Validate source store
        const origin = await transactionalEntityManager.findOne(Store, {
          where: { id: sourceStore, companyId },
        });
        if (!origin) {
          logError(
            companyId,
            "Source store not found",
            "INSTANT_TRANSFER_SOURCE_NOT_FOUND",
            JSON.stringify({ sourceStore }),
            "medium",
            `Instant transfer failed - source store not found: ${sourceStore}`,
            userId
          );
          throw new Error("Source store not found.");
        }

        // Validate destination store
        const destination = await transactionalEntityManager.findOne(Store, {
          where: { id: destinationStore, companyId },
        });
        if (!destination) {
          logError(
            companyId,
            "Destination store not found",
            "INSTANT_TRANSFER_DESTINATION_NOT_FOUND",
            JSON.stringify({ destinationStore }),
            "medium",
            `Instant transfer failed - destination store not found: ${destinationStore}`,
            userId
          );
          throw new Error("Destination store not found.");
        }
        if (origin.id === destination.id) {
          logError(
            companyId,
            "Same store transfer attempt",
            "INSTANT_TRANSFER_SAME_STORE",
            JSON.stringify({ storeId: origin.id }),
            "low",
            `Instant transfer failed - attempted transfer within same store: ${origin.id}`,
            userId
          );
          throw new Error("Cannot transfer items within the same store.");
        }

        // Process each transfer
        for (const transfer of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
            relations: ["units"], // Add units relation
          });

          if (!item) {
            logError(
              companyId,
              "Item not found",
              "INSTANT_TRANSFER_ITEM_NOT_FOUND",
              JSON.stringify({ itemId: transfer.itemId }),
              "medium",
              `Instant transfer failed - item not found: ${transfer.itemId}`,
              userId
            );
            throw new Error(`Item with ID ${transfer.itemId} not found.`);
          }

          // Calculate transfer quantity with improved unit conversion
          let standardQuantity: number;
          if (!transfer.unit || transfer.unit === item.unit) {
            standardQuantity = transfer.quantity;
          } else {
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item.id,
                companyId,
                name: transfer.unit,
              },
            });

            if (!customUnit) {
              logError(
                companyId,
                "Invalid unit",
                "INSTANT_TRANSFER_INVALID_UNIT",
                JSON.stringify({ itemId: item.id, unit: transfer.unit }),
                "medium",
                `Instant transfer failed - invalid unit for item: ${item.id}`,
                userId
              );
              throw new Error(
                `Invalid unit '${transfer.unit}' for ${item.name}.`
              );
            }
            standardQuantity = transfer.quantity * customUnit.quantity;
          }

          if (standardQuantity <= 0) {
            logError(
              companyId,
              "Invalid quantity",
              "INSTANT_TRANSFER_INVALID_QUANTITY",
              JSON.stringify({ itemId: item.id, quantity: standardQuantity }),
              "medium",
              `Instant transfer failed - invalid quantity for item: ${item.id}`,
              userId
            );
            throw new Error(`Invalid transfer quantity for ${item.name}.`);
          }

          // Check batch stock if specified
          if (transfer.batch) {
            const batch = await transactionalEntityManager.findOne(BatchStock, {
              where: { itemId: item.id, batch: transfer.batch, companyId },
            });

            if (!batch || batch.stock < standardQuantity) {
              logError(
                companyId,
                "Insufficient batch stock",
                "INSTANT_TRANSFER_INSUFFICIENT_BATCH_STOCK",
                JSON.stringify({
                  itemId: item.id,
                  batch: transfer.batch,
                  required: standardQuantity,
                  available: batch?.stock,
                }),
                "medium",
                `Instant transfer failed - insufficient batch stock for item: ${item.id}`,
                userId
              );
              throw new Error(
                `Insufficient stock for ${item.name} in batch ${transfer.batch}.`
              );
            }
          }

          // Check source store stock with standardQuantity
          const sourceStoreItemStock = await transactionalEntityManager.findOne(
            StoreItemStock,
            {
              where: {
                itemId: item.id,
                storeId: origin.id,
                companyId,
              },
            }
          );

          if (
            !sourceStoreItemStock ||
            sourceStoreItemStock.stock < standardQuantity
          ) {
            logError(
              companyId,
              "Insufficient store stock",
              "INSTANT_TRANSFER_INSUFFICIENT_STORE_STOCK",
              JSON.stringify({
                itemId: item.id,
                storeId: origin.id,
                required: standardQuantity,
                available: sourceStoreItemStock?.stock,
              }),
              "medium",
              `Instant transfer failed - insufficient stock in source store for item: ${item.id}`,
              userId
            );
            throw new Error(
              `Insufficient ${item.name} stock in ${origin.name}.`
            );
          }
        }

        // Create Inventory record
        const inventoryTransfer = await transactionalEntityManager
          .create(Inventory, {
            companyId,
            type: "transfer",
            transferDate: new Date(),
            details: "Instant Transfer",
            sourceStoreId: origin.id,
            destinationStoreId: destination.id,
            granted: true,
            dispatched: true,
            received: true,
            consumerId: employee?.id,
          })
          .save();

        const transferRecords: Transfer[] = [];
        const transferItems: Item[] = [];

        // Process transfers and update stocks
        for (const transfer of args) {
          const item = await transactionalEntityManager.findOne(Item, {
            where: { id: transfer.itemId, companyId },
          });

          // Recalculate standardQuantity for actual transfer
          let standardQuantity: number;
          if (!transfer.unit || transfer.unit === item!.unit) {
            standardQuantity = transfer.quantity;
          } else {
            const customUnit = await transactionalEntityManager.findOne(Unit, {
              where: {
                itemId: item!.id,
                companyId,
                name: transfer.unit,
              },
            });
            standardQuantity = transfer.quantity * customUnit!.quantity;
          }

          // Handle batch transfers
          if (transfer.batch) {
            const batch = await transactionalEntityManager.findOne(BatchStock, {
              where: { itemId: item!.id, batch: transfer.batch, companyId },
            });

            // Update source store batch stock
            let sourceStoreItemStock = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  itemId: item!.id,
                  storeId: origin.id,
                  batchId: batch!.id,
                  companyId,
                },
                relations: ["item", "store", "batchStock"],
              }
            );

            sourceStoreItemStock!.stock =
              sourceStoreItemStock!.stock - standardQuantity;
            await transactionalEntityManager.save(sourceStoreItemStock);

            // Update or create destination store batch stock
            let destinationStoreItemStock =
              await transactionalEntityManager.findOne(StoreItemStock, {
                where: {
                  companyId,
                  itemId: item!.id,
                  storeId: destination.id,
                  batchId: batch!.id,
                },
                relations: ["item", "store", "batchStock"],
              });

            if (!destinationStoreItemStock) {
              destinationStoreItemStock = transactionalEntityManager.create(
                StoreItemStock,
                {
                  item: item!,
                  companyId,
                  store: destination,
                  stock: standardQuantity,
                  batchId: batch!.id,
                  batchStock: batch!,
                }
              );
            } else {
              destinationStoreItemStock.stock =
                destinationStoreItemStock.stock + standardQuantity;
            }
            await transactionalEntityManager.save(destinationStoreItemStock);

            // Update batch stock
            batch!.stock = batch!.stock - standardQuantity;
            await transactionalEntityManager.save(batch);
          } else {
            // Handle non-batch transfers
            // Update source store stock
            let sourceStoreItemStock = await transactionalEntityManager.findOne(
              StoreItemStock,
              {
                where: {
                  itemId: item!.id,
                  storeId: origin.id,
                  batchId: null,
                  companyId,
                },
                relations: ["item", "store"],
              }
            );

            sourceStoreItemStock!.stock =
              sourceStoreItemStock!.stock - standardQuantity;
            await transactionalEntityManager.save(sourceStoreItemStock);

            // Update or create destination store stock
            let destinationStoreItemStock =
              await transactionalEntityManager.findOne(StoreItemStock, {
                where: {
                  companyId,
                  itemId: item!.id,
                  storeId: destination.id,
                  batchId: null,
                },
                relations: ["item", "store"],
              });

            if (!destinationStoreItemStock) {
              destinationStoreItemStock = transactionalEntityManager.create(
                StoreItemStock,
                {
                  item: item!,
                  companyId,
                  store: destination,
                  stock: standardQuantity,
                }
              );
            } else {
              destinationStoreItemStock.stock =
                destinationStoreItemStock.stock + standardQuantity;
            }
            await transactionalEntityManager.save(destinationStoreItemStock);
          }

          // Create Transfer record
          const transferRecord = transactionalEntityManager.create(Transfer, {
            inventoryId: inventoryTransfer.id,
            companyId,
            item: item!,
            quantity: standardQuantity, // Store the standard quantity
            details: "",
            batch: transfer.batch,
            granted: true,
            dispatched: true,
            received: true,
          });
          transferRecords.push(transferRecord);
          transferItems.push(item!);
        }

        // Link transfers and items to inventory
        inventoryTransfer.transfers = transferRecords;
        inventoryTransfer.items = transferItems;
        await transactionalEntityManager.save(inventoryTransfer);

        // Save all transfer records
        await Promise.all(
          transferRecords.map((record) =>
            transactionalEntityManager.save(record)
          )
        );
      });

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "INSTANT_TRANSFER_ERROR",
        JSON.stringify({
          error: err,
          sourceStore,
          destinationStore,
          transfers: args,
        }),
        "high",
        `Instant transfer failed - unexpected error`,
        userId
      );
      console.error(err.message);
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
  }

  //writeoffs only query
  @Query(() => [Transfer])
  @UseMiddleware(isAuth)
  async getWriteOffsByCompany(@Ctx() { req }: MyContext): Promise<Transfer[]> {
    const companyId = Number(req.params.companyId);
    const userId = req.session.userId;

    try {
      const inventories = await Inventory.find({
        where: { companyId, type: "writeOff" },
        relations: ["transfers"],
      });

      if (!inventories.length) {
        logError(
          companyId,
          "No writeoffs found",
          "GET_WRITEOFFS_EMPTY",
          JSON.stringify({ companyId }),
          "low",
          `Get writeoffs returned empty result for company: ${companyId}`,
          userId
        );
        return [];
      }

      let writeOffs: number[] = [];
      inventories.forEach((inventory) => {
        return inventory.transfers.forEach((transfer) =>
          writeOffs.push(transfer.id)
        );
      });

      const transferRepository = getRepository(Transfer);
      return transferRepository
        .createQueryBuilder("Transfer")
        .leftJoinAndSelect("Transfer.item", "item")
        .where("Transfer.id IN (:...ids) AND Transfer.companyId = :companyId", {
          ids: writeOffs,
          companyId: companyId,
        })
        .orderBy("Transfer.createdAt", "DESC")
        .getMany();
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_WRITEOFFS_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to fetch writeoffs for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  // get dispatches
  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getDispatches(@Ctx() { req }: MyContext): Promise<Inventory[]> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    try {
      const inventories = await Inventory.find({
        where: { companyId, type: "dispatch" },
        relations: ["transfers", "items", "sourceStore", "destinationStore"],
        order: { transferDate: "DESC" },
      });

      if (!inventories.length) {
        logError(
          companyId,
          "No dispatches found",
          "GET_DISPATCHES_EMPTY",
          JSON.stringify({ companyId }),
          "low",
          `Get dispatches returned empty result for company: ${companyId}`,
          userId
        );
      }

      return inventories;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_DISPATCHES_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to fetch dispatches for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  // get get quick sales (filter for today sales only)
  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getSales(
    @Arg("date", () => Date, { nullable: true }) date: Date | null,
    @Ctx() { req }: MyContext
  ): Promise<Inventory[]> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    try {
      let startDate: Date;
      let endDate: Date;

      if (date) {
        // If date is provided, set range from that date to a day before same date next month
        startDate = startOfDay(date);
        if (date.getDate() === 1) {
          // If it's 1st of month, get full month
          endDate = endOfDay(endOfMonth(date));
        } else {
          // Otherwise, get until day before same date next month
          endDate = endOfDay(subDays(addMonths(date, 1), 1));
        }
      } else {
        // If no date provided, get current month
        startDate = startOfMonth(new Date());
        endDate = endOfMonth(new Date());
      }

      const inventories = await Inventory.find({
        where: {
          type: "sale",
          createdAt: Between(startDate, endDate),
          companyId,
        },
        order: { updatedAt: "DESC" },
        relations: [
          "transfers",
          "items",
          "items.imports",
          "sourceStore",
          "bill",
          "consumer",
          "consumer.user",
          "keeper",
          "keeper.user",
        ],
      });

      if (!inventories.length) {
        logError(
          companyId,
          "No sales found",
          "GET_SALES_EMPTY",
          JSON.stringify({ companyId, startDate, endDate }),
          "low",
          `Get sales returned empty result for company: ${companyId}`,
          userId
        );
      }

      return inventories;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_SALES_ERROR",
        JSON.stringify({ error: err, date }),
        "high",
        `Failed to fetch sales for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getSalesPOS(@Ctx() { req }: MyContext): Promise<Inventory[]> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    // Get the start and end of today
    const startOfToday = startOfDay(new Date());
    const endOfToday = endOfDay(new Date());

    try {
      const inventories = await Inventory.find({
        where: {
          companyId,
          type: "sale",
          createdAt: Between(startOfToday, endOfToday),
        },
        order: { updatedAt: "DESC" },
        relations: [
          "transfers",
          "items",
          "sourceStore",
          "bill",
          "consumer",
          "consumer.user",
          "keeper",
          "keeper.user",
        ],
      });

      if (!inventories.length) {
        logError(
          companyId,
          "No POS sales found for today",
          "GET_SALES_POS_EMPTY",
          JSON.stringify({ companyId, startOfToday, endOfToday }),
          "low",
          `Get POS sales returned empty result for company: ${companyId}`,
          userId
        );
      }

      return inventories;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_SALES_POS_ERROR",
        JSON.stringify({ error: err, dateRange: { startOfToday, endOfToday } }),
        "high",
        `Failed to fetch POS sales for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getOpenTabs(@Ctx() { req }: MyContext): Promise<Inventory[]> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    try {
      const inventories = await Inventory.find({
        where: {
          companyId,
          type: "sale",
          deleted: false,
        },
        order: { updatedAt: "DESC" },
        relations: [
          "transfers",
          "items",
          "sourceStore",
          "bill",
          "consumer",
          "consumer.user",
          "keeper",
          "keeper.user",
        ],
      });

      const responseInventories = inventories.filter((inventory) => {
        if (inventory.bill) return inventory.bill.cleared === false;
        return false;
      });

      if (!responseInventories.length) {
        logError(
          companyId,
          "No open tabs found",
          "GET_OPEN_TABS_EMPTY",
          JSON.stringify({ companyId }),
          "low",
          `Get open tabs returned empty result for company: ${companyId}`,
          userId
        );
      }

      return responseInventories;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_OPEN_TABS_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to fetch open tabs for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  // get transfers
  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getTransfers(@Ctx() { req }: MyContext): Promise<Inventory[]> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    try {
      const inventories = await Inventory.find({
        where: { companyId, type: "transfer" },
        relations: ["transfers", "items", "sourceStore", "destinationStore"],
        order: { updatedAt: "DESC" },
      });

      if (!inventories.length) {
        logError(
          companyId,
          "No transfers found",
          "GET_TRANSFERS_EMPTY",
          JSON.stringify({ companyId }),
          "low",
          `Get transfers returned empty result for company: ${companyId}`,
          userId
        );
      }

      return inventories;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_TRANSFERS_ERROR",
        JSON.stringify(err),
        "high",
        `Failed to fetch transfers for company: ${companyId}`,
        userId
      );
      throw err;
    }
  }

  //ADD ITEMS FROM EXCEL
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async addItemsFromExcel(
    @Arg("args", () => BulkItemInput) args: BulkItemInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;
    const items = args.items;

    for (const item of items) {
      if (!item.name || !item.type || !item.unit) {
        logError(
          companyId,
          "Invalid item data in Excel import",
          "ADD_ITEMS_EXCEL_VALIDATION",
          JSON.stringify(item),
          "medium",
          "Required fields missing in Excel import item",
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "Fields can not be empty!",
          },
        };
      }
    }

    try {
      await Promise.all(
        items.map((item) =>
          Item.create({
            ...item,
            companyId: req.session.companyId,
          }).save()
        )
      );
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ADD_ITEMS_EXCEL_ERROR",
        JSON.stringify({ error: err, items }),
        "high",
        `Failed to import items from Excel for company: ${companyId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }

    return {
      status: true,
    };
  }

  //EDIT ITEMS TO BE ABLE TO HAVEin the  INVENTORY
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editItem(
    @Arg("args", () => ItemInput) args: ItemInput,
    @Arg("id", () => Number) id: Number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (
      !args.name ||
      !args.type ||
      !args.unit ||
      (args.barcode && args.barcode.trim() === "")
    ) {
      logError(
        companyId,
        "Invalid item edit data",
        "EDIT_ITEM_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Required fields missing or barcode is empty in item edit",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message:
            "Fields can not be empty and barcode cannot be an empty string!",
        },
      };
    }
    try {
      let item = await Item.findOne({
        where: { id: id, companyId: req.session.companyId },
      });
      if (item) {
        item.name = args.name;
        item.image = args.image;
        item.description = args.description;
        item.reorder = args.reorder;
        item.type = args.type;
        item.reference = args.reference;
        item.reorder = args.reorder;
        item.unit = args.unit;
        item.sellingPrice = args.sellingPrice;
        item.save();
      } else {
        logError(
          companyId,
          "Item not found",
          "EDIT_ITEM_NOT_FOUND",
          JSON.stringify({ itemId: id, companyId }),
          "medium",
          `Item not found during edit operation`,
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "Item could not be found!",
          },
        };
      }
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EDIT_ITEM_ERROR",
        JSON.stringify({ error: err, itemId: id, args }),
        "high",
        `Failed to edit item for company: ${companyId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return {
      status: true,
    };
  }

  //EDIT ITEMS TO BE ABLE TO HAVEin the  INVENTORY
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editService(
    @Arg("args", () => ServiceInput) args: ServiceInput,
    @Arg("id", () => Number) id: Number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    if (!args.name || !args.sellingPrice || args.sellingPrice <= 0) {
      logError(
        companyId,
        "Invalid service edit data",
        "EDIT_SERVICE_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Required fields missing or invalid in service edit",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Some fields can not be empty!",
        },
      };
    }
    try {
      let item = await Item.findOne({
        where: { id: id, companyId: req.session.companyId },
      });
      if (item) {
        item.name = args.name;
        item.description = args.description;
        item.reference = args.reference;
        item.sellingPrice = args.sellingPrice;
        item.save();
      } else {
        logError(
          companyId,
          "Service not found",
          "EDIT_SERVICE_NOT_FOUND",
          JSON.stringify({ serviceId: id, companyId }),
          "medium",
          `Service not found during edit operation`,
          userId
        );
        return {
          status: false,
          error: {
            target: "general",
            message: "Item could not be found!",
          },
        };
      }
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EDIT_SERVICE_ERROR",
        JSON.stringify({ error: err, serviceId: id, args }),
        "high",
        `Failed to edit service for company: ${companyId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }
    return {
      status: true,
    };
  }

  //EDIT STORE
  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async editStore(
    @Arg("args", () => StoreEditInput) args: StoreEditInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        const store = await transactionalEntityManager.findOne(Store, {
          where: { id: args.id, companyId },
        });
        if (!store) {
          logError(
            companyId,
            "Store not found",
            "EDIT_STORE_NOT_FOUND",
            JSON.stringify({ storeId: args.id, companyId }),
            "medium",
            `Store not found during edit operation`,
            userId
          );
          throw new Error("Store not found.");
        }

        if (args.primary && store.primary !== args.primary) {
          const primaryStoreCount = await transactionalEntityManager.count(
            Store,
            {
              where: { companyId, primary: true },
            }
          );

          if (primaryStoreCount > 0) {
            logError(
              companyId,
              "Multiple primary stores attempted",
              "EDIT_STORE_MULTIPLE_PRIMARY",
              JSON.stringify({ storeId: args.id, companyId }),
              "medium",
              `Attempted to set multiple primary stores`,
              userId
            );
            throw new Error("There can only be one primary store per company.");
          }
        }

        if (args.name) store.name = args.name;
        if (args.primary !== undefined) store.primary = args.primary;
        if (args.address) store.address = args.address;

        await transactionalEntityManager.save(store);
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "EDIT_STORE_ERROR",
        JSON.stringify({ error: err, storeId: args.id, args }),
        "high",
        `Failed to edit store for company: ${companyId}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }

    return { status: true };
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async importItem(
    @Arg("args", () => ImportInput) args: ImportInput,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = Number(req.session.companyId);
    const userId = req.session.userId;

    if (args.importPrice === 0 || args.quantity === 0) {
      logError(
        companyId,
        "Invalid import values",
        "IMPORT_ITEM_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Import price or quantity cannot be zero",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Fields cannot be zero!",
        },
      };
    }

    // Validate that either itemId or barcode is provided
    if (!args.itemId && !args.barcode) {
      logError(
        companyId,
        "Missing item identification",
        "IMPORT_ITEM_VALIDATION",
        JSON.stringify(args),
        "medium",
        "Either itemId or barcode must be provided",
        userId
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Either item ID or barcode must be provided!",
        },
      };
    }

    try {
      await getConnection().transaction(async (transactionalEntityManager) => {
        // Find the item to be imported.
        const item = await transactionalEntityManager.findOne(Item, {
          where: { id: args.itemId, companyId },
        });
        if (!item) {
          logError(
            companyId,
            "Item not found",
            "IMPORT_ITEM_NOT_FOUND",
            JSON.stringify({ itemId: args.itemId }),
            "high",
            "Item does not exist for import",
            userId
          );
          throw new Error("Item does not exist!");
        }

        const employee = await transactionalEntityManager.findOne(Employee, {
          where: { userId: req.session.userId, companyId },
        });
        if (!employee) {
          logError(
            companyId,
            "Employee not found",
            "IMPORT_EMPLOYEE_NOT_FOUND",
            JSON.stringify({ userId }),
            "high",
            "Employee does not exist for import operation",
            userId
          );
          throw new Error("Employee does not exist!");
        }

        // Determine the stock quantity only if the unit matches.
        const stockQuantity = args.unit === item.unit ? args.quantity : 0;

        // Verify company existence.
        const company = await transactionalEntityManager.findOne(
          Company,
          companyId
        );
        if (!company) {
          logError(
            companyId,
            "Company not found",
            "IMPORT_COMPANY_NOT_FOUND",
            JSON.stringify({ companyId }),
            "critical",
            "Company does not exist for import operation",
            userId
          );
          throw new Error("Your company does not exist!");
        }

        // Get the primary store.
        const store = await transactionalEntityManager.findOne(Store, {
          where: { companyId, primary: true },
        });
        if (!store) {
          logError(
            companyId,
            "Primary store not found",
            "IMPORT_STORE_NOT_FOUND",
            JSON.stringify({ companyId }),
            "high",
            "No primary store found for import operation",
            userId
          );
          throw new Error("No store location identified!");
        }

        // Create and save the import record.
        const newImport = transactionalEntityManager.create(Import, {
          ...args,
          item,
          companyId,
        });
        await transactionalEntityManager.save(newImport);

        // Handle batch stock if a batch is provided.
        let batchStock;
        if (args.batch) {
          batchStock = await transactionalEntityManager.findOne(BatchStock, {
            where: { itemId: args.itemId, batch: args.batch, companyId },
          });
          if (!batchStock) {
            batchStock = transactionalEntityManager.create(BatchStock, {
              item,
              companyId,
              batch: args.batch,
              stock: stockQuantity,
              expireDate: args.expireDate,
            });
          } else {
            batchStock.stock = batchStock.stock + stockQuantity;
            batchStock.expireDate = args.expireDate;
          }
          await transactionalEntityManager.save(batchStock);
        }

        // Find or create the store item stock record.
        let storeItem = await transactionalEntityManager.findOne(
          StoreItemStock,
          {
            where: {
              companyId,
              itemId: args.itemId,
              storeId: store.id,
              batchId: batchStock ? batchStock.id : null,
            },
          }
        );

        if (!storeItem) {
          storeItem = transactionalEntityManager.create(StoreItemStock, {
            item,
            companyId,
            batchId: batchStock ? batchStock.id : undefined,
            store,
            stock: stockQuantity,
          });
        } else {
          storeItem.stock = storeItem.stock + stockQuantity;
        }
        await transactionalEntityManager.save(storeItem);

        // Update the overall item stock and selling price.
        item.stock = item.stock + stockQuantity;
        item.sellingPrice = args.sellingPrice ?? 0;
        await transactionalEntityManager.save(item);

        // Create and save the inventory transfer record.
        const newInventory = transactionalEntityManager.create(Inventory, {
          companyId,
          details: "Imported item",
          type: "purchase",
          granted: true,
          received: true,
          transferDate: args.importDate,
          destinationStoreId: store.id,
          consumerId: employee.id,
        });
        await transactionalEntityManager.save(newInventory);

        // Create and save the transfer record.
        const newTransfer = transactionalEntityManager.create(Transfer, {
          companyId,
          inventoryId: newInventory.id,
          itemId: item.id,
          batch: batchStock ? batchStock.batch : undefined,
          quantity: stockQuantity,
          price: args.importPrice,
          granted: true,
          received: true,
          dispatched: true,
          details: "Item import transfer",
        });
        await transactionalEntityManager.save(newTransfer);
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "IMPORT_ITEM_ERROR",
        JSON.stringify({ error: err, args }),
        "high",
        `Failed to import item: ${err.message}`,
        userId
      );
      return {
        status: false,
        error: { target: "general", message: err.message },
      };
    }

    return { status: true };
  }

  // QUERY ALL internal ITEMS
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getInternalItems(@Ctx() { req }: MyContext): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes: Item[] = await Item.find({
        where: {
          companyId: req.session.companyId,
          internal: true,
          type: Not("service"),
        },
        order: { name: "ASC" },
        relations: ["units"],
      });
      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_INTERNAL_ITEMS_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch internal items",
        userId
      );
      return [];
    }
  }

  // QUERY ALL merchandise ITEMS
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getMerchandiseItems(@Ctx() { req }: MyContext): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes: Item[] = await Item.find({
        where: {
          companyId: req.session.companyId,
          internal: false,
          type: Not("service"),
        },
        order: { name: "ASC" },
        relations: ["units"],
      });
      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_MERCHANDISE_ITEMS_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch merchandise items",
        userId
      );
      return [];
    }
  }

  // QUERY ALL ITEMS
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getAllItems(@Ctx() { req }: MyContext): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes: Item[] = await Item.find({
        where: { companyId: req.session.companyId, type: Not("service") },
        order: { name: "ASC" },
        relations: ["units"],
      });
      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ALL_ITEMS_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch all items",
        userId
      );
      return [];
    }
  }

  // QUERY ALL SERVICES
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getAllServices(@Ctx() { req }: MyContext): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes: Item[] = await Item.find({
        where: { companyId: req.session.companyId, type: "service" },
        order: { name: "ASC" },
      });
      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ALL_SERVICES_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch all services",
        userId
      );
      return [];
    }
  }

  // QUERY ALL ITEMS FOR ONE STORE
  @Query(() => [Item])
  @UseMiddleware(isAuth)
  async getStoreItems(
    @Arg("storeId") storeId: number,
    @Ctx() { req }: MyContext
  ): Promise<Item[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      // Step 1: Fetch all items for the company
      let items = await getRepository(Item).find({
        where: { companyId: req.session.companyId, type: Not("service") },
        order: { name: "ASC" },
        relations: ["units"],
      });

      // Step 2: Fetch store item stocks for the specific store
      const storeItemStocks = await getRepository(StoreItemStock).find({
        where: { storeId, companyId: req.session.companyId },
      });

      // Step 3: Adjust stock counts based on the store item stocks
      const modifieditems = items.map((item) => {
        const stockData = storeItemStocks.filter(
          (stock) => stock.itemId === item.id
        );

        const totalStock = stockData.reduce(
          (total, stock) => total + stock.stock,
          0
        );

        return {
          ...item,
          stock: totalStock,
        };
      });

      return modifieditems as Item[];
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_STORE_ITEMS_ERROR",
        JSON.stringify({ error: err, storeId }),
        "high",
        "Failed to fetch store items",
        userId
      );
      return [];
    }
  }

  // get imports by batch
  @Query(() => [Import])
  @UseMiddleware(isAuth)
  async getItemBatchImports(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext
  ): Promise<Import[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const imports = await Import.find({
        where: {
          itemId,
          companyId: req.session.companyId,
        },
        order: {
          importDate: "DESC",
        },
      });

      return imports;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_BATCH_IMPORTS_ERROR",
        JSON.stringify({ error: err, itemId }),
        "high",
        "Failed to fetch item batch imports",
        userId
      );
      return [];
    }
  }

  //get compony today imports
  @Query(() => [Import])
  @UseMiddleware(isAuth)
  async getTodaysImports(@Ctx() { req }: MyContext): Promise<Import[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const startOfToday = startOfDay(new Date());
      const endOfToday = endOfDay(new Date());

      const imports = await Import.find({
        where: {
          companyId: req.session.companyId,
          importDate: Between(startOfToday, endOfToday),
        },
        order: {
          importDate: "DESC",
        },
        relations: ["item"],
      });

      return imports;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_TODAYS_IMPORTS_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch today's imports",
        userId
      );
      return [];
    }
  }

  // QUERY ALL ITEMS
  @Query(() => [Store])
  @UseMiddleware(isAuth)
  async getStores(@Ctx() { req }: MyContext): Promise<Store[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes: Store[] = await Store.find({
        where: { companyId: req.session.companyId },
        order: { name: "ASC" },
        relations: ["storeKeepers"],
      });
      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_STORES_ERROR",
        JSON.stringify(err),
        "high",
        "Failed to fetch stores",
        userId
      );
      return [];
    }
  }

  @Query(() => [BatchStock])
  @UseMiddleware(isAuth)
  async getItemBatchStocks(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext
  ): Promise<BatchStock[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const batches = await BatchStock.find({
        where: [
          { itemId, companyId: req.session.companyId, stock: MoreThan(0) },
        ],
        relations: ["storeItemStocks"],
      });
      return batches;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_BATCH_STOCKS_ERROR",
        JSON.stringify({ error: err, itemId }),
        "high",
        "Failed to fetch item batch stocks",
        userId
      );
      return [];
    }
  }

  @Query(() => [BatchStock])
  @UseMiddleware(isAuth)
  async getBatchStockForStore(
    @Ctx() { req }: MyContext,
    @Arg("itemId", { nullable: true }) itemId?: number,
    @Arg("storeId", { nullable: true }) storeId?: number
  ): Promise<BatchStock[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let batches: BatchStock[];
      if (storeId && itemId) {
        batches = await BatchStock.find({
          where: [
            { itemId, companyId: req.session.companyId, stock: MoreThan(0) },
          ],
          relations: ["storeItemStocks"],
        });
        batches = batches.filter((batch) =>
          batch.storeItemStocks.filter((stock) => stock.storeId === storeId)
        );
      } else if (itemId) {
        batches = await BatchStock.find({
          where: [
            { itemId, companyId: req.session.companyId, stock: MoreThan(0) },
          ],
          relations: ["storeItemStocks"],
        });
      } else if (storeId) {
        batches = await BatchStock.find({
          where: [
            { storeId, companyId: req.session.companyId, stock: MoreThan(0) },
          ],
          relations: ["storeItemStocks"],
        });
        batches = batches.filter((batch) =>
          batch.storeItemStocks.filter((stock) => stock.storeId === storeId)
        );
      } else {
        batches = await BatchStock.find({
          where: [{ companyId: req.session.companyId, stock: MoreThan(0) }],
          relations: ["storeItemStocks"],
        });
      }
      return batches;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_BATCH_STOCK_FOR_STORE_ERROR",
        JSON.stringify({ error: err, itemId, storeId }),
        "high",
        "Failed to fetch batch stock for store",
        userId
      );
      return [];
    }
  }

  @Query(() => [StoreItemStock])
  @UseMiddleware(isAuth)
  async getItemStoreStocks(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext
  ): Promise<StoreItemStock[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const batches = await StoreItemStock.find({
        where: [
          { itemId, companyId: req.session.companyId, stock: MoreThan(0) },
        ],
        relations: ["store"],
      });
      return batches;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_STORE_STOCKS_ERROR",
        JSON.stringify({ error: err, itemId }),
        "high",
        "Failed to fetch item store stocks",
        userId
      );
      return [];
    }
  }

  // QUERY ALL ITEMS
  @Query(() => Item)
  @UseMiddleware(isAuth)
  async getItem(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<Item | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Item.findOne({
        where: { id, companyId: req.session.companyId },
        relations: ["imports", "transfers", "units"],
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_ERROR",
        JSON.stringify({ error: err, itemId: id }),
        "high",
        "Failed to fetch item",
        userId
      );
      return undefined;
    }
  }
  // QUERY AN ITEM BY BARCODE
  @Query(() => Item, { nullable: true })
  @UseMiddleware(isAuth)
  async getItemByBarcode(
    @Arg("barcode") barcode: string,
    @Ctx() { req }: MyContext
  ): Promise<Item | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      return await Item.findOne({
        where: { barcode, companyId: req.session.companyId },
        relations: ["imports", "transfers", "units"],
      });
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_BY_BARCODE_ERROR",
        JSON.stringify({ error: err, barcode }),
        "high",
        "Failed to fetch item by barcode",
        userId
      );
      return undefined;
    }
  }

  // QUERY ALL INVENTORY TRANSFERS
  @Query(() => [Inventory])
  @UseMiddleware(isAuth)
  async getInventoryTransfers(
    @Ctx() { req }: MyContext,
    @Arg("type", () => String, { nullable: true, validate: false })
    type?:
      | "purchase"
      | "dispatch"
      | "sale"
      | "transfer"
      | "writeOff"
      | "returnToVendor"
  ): Promise<Inventory[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const whereClause = type ? { companyId, type } : { companyId };

      const reqRes: Inventory[] = await Inventory.find({
        where: whereClause,
        order: { createdAt: "DESC" },
      });

      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_INVENTORY_TRANSFERS_ERROR",
        JSON.stringify({ error: err, type }),
        "high",
        "Failed to fetch inventory transfers",
        userId
      );
      return [];
    }
  }

  // QUERY ALL INVENTORY TRANSFERS
  @Query(() => [Transfer])
  @UseMiddleware(isAuth)
  async getItemTransfers(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext,
    @Arg("type", () => String, { nullable: true })
    type?:
      | "purchase"
      | "dispatch"
      | "sale"
      | "transfer"
      | "writeOff"
      | "returnToVendor"
  ): Promise<Transfer[]> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      let reqRes = await Transfer.find({
        where: { itemId: itemId, companyId: req.session.companyId },
        relations: ["inventoryTransfer", "item"],
        order: {
          updatedAt: "DESC",
        },
      });
      reqRes = reqRes.filter(
        (trans) => trans.inventoryTransfer.received === true
      );

      if (type)
        reqRes = reqRes.filter(
          (trans) => trans.inventoryTransfer.type === type
        );

      return reqRes;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_ITEM_TRANSFERS_ERROR",
        JSON.stringify({ error: err, itemId, type }),
        "high",
        "Failed to fetch item transfers",
        userId
      );
      return [];
    }
  }

  @Query(() => Inventory, { nullable: true })
  @UseMiddleware(isAuth)
  async getInventoryTransfer(
    @Arg("id", () => Number) id: number,
    @Ctx() { req }: MyContext
  ): Promise<Inventory | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const inventoryTransfer = await Inventory.findOne({
        where: { id, companyId: req.session.companyId },
        relations: ["transfers", "items"],
      });

      if (!inventoryTransfer) {
        throw new Error("Inventory transfer not found.");
      }

      return inventoryTransfer;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GET_INVENTORY_TRANSFER_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        "Failed to fetch inventory transfer",
        userId
      );
      return undefined;
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async deleteItem(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const item = await Item.findOne({
        where: { id, companyId: req.session.companyId },
      });
      if (!item) {
        logError(
          companyId,
          "Item does not exist",
          "DELETE_ITEM_ERROR",
          JSON.stringify({ id }),
          "high",
          "Failed to delete non-existent item",
          userId
        );
        return {
          status: false,
          error: { target: "general", message: "Item does not exist!" },
        };
      }
      await Item.delete({ id, companyId: req.session.companyId });
      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "DELETE_ITEM_ERROR",
        JSON.stringify({ error: err, id }),
        "high",
        "Failed to delete item",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "Item was not deleted!" },
      };
    }
  }

  @Mutation(() => Item, { nullable: true })
  @UseMiddleware(isAuth)
  async generateBarcode(
    @Arg("itemId") itemId: number,
    @Ctx() { req }: MyContext
  ): Promise<Item | undefined> {
    const companyId = req.session.companyId;
    const userId = req.session.userId;

    try {
      const item = await Item.findOne({
        where: { id: itemId, companyId },
      });

      if (!item) {
        logError(
          companyId,
          "Item not found",
          "GENERATE_BARCODE_ERROR",
          JSON.stringify({ itemId }),
          "high",
          "Failed to generate barcode for non-existent item",
          userId
        );
        throw new Error("Item not found");
      }

      if (item.barcode) {
        return item;
      }

      const barcode = Math.random().toString(36).substring(2, 15);
      item.barcode = barcode;
      await item.save();

      return item;
    } catch (err) {
      logError(
        companyId,
        err.message,
        "GENERATE_BARCODE_ERROR",
        JSON.stringify({ error: err, itemId }),
        "high",
        "Failed to generate barcode",
        userId
      );
      return undefined;
    }
  }

  @Mutation(() => BooleanResponse)
  @UseMiddleware(isAuth)
  async assignStoreKeeper(
    @Arg("storeId") storeId: number,
    @Arg("userId") userId: number,
    @Ctx() { req }: MyContext
  ): Promise<BooleanResponse> {
    const companyId = req.session.companyId;
    try {
      const store = await Store.findOne({
        where: { id: storeId, companyId: req.session.companyId },
      });
      if (!store) {
        logError(
          companyId,
          "Store not found",
          "ASSIGN_STORE_KEEPER_ERROR",
          JSON.stringify({ storeId, userId }),
          "high",
          "Failed to assign store keeper - store not found",
          userId
        );
        return {
          status: false,
          error: { target: "store", message: "Store not found" },
        };
      }

      const employee = await Employee.findOne({
        where: { userId: userId, companyId: req.session.companyId },
      });
      if (!employee) {
        logError(
          companyId,
          "Employee not found",
          "ASSIGN_STORE_KEEPER_ERROR",
          JSON.stringify({ storeId, userId }),
          "high",
          "Failed to assign store keeper - employee not found",
          userId
        );
        return {
          status: false,
          error: { target: "employee", message: "Employee not found" },
        };
      }

      // USE QUERY BUILDER TO ADD EMPLOYEE TO STORE
      await getConnection()
        .createQueryBuilder()
        .relation(Store, "storeKeepers")
        .of(store)
        .add(employee);

      return { status: true };
    } catch (err) {
      logError(
        companyId,
        err.message,
        "ASSIGN_STORE_KEEPER_ERROR",
        JSON.stringify({ error: err, storeId, userId }),
        "high",
        "Failed to assign store keeper",
        userId
      );
      return {
        status: false,
        error: { target: "general", message: "Store keeper was not assigned!" },
      };
    }
  }
}
