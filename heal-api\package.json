{"$schema": "https://json.schemastore.org/package.json", "name": "halisia-server", "version": "0.1.0", "description": "", "type": "commonjs", "main": "./dist/index.js", "scripts": {"watch": "tsc -w", "dev": "nodemon dist/index.js", "dev2": "nodemon --exec dist/index.js", "start": "node dist/index.js", "start2": "ts-node src/index.ts", "build": "tsc", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js", "migration:create": "npm run typeorm migration:create -- -n", "migration:run": "npm run typeorm migration:run", "migration:revert": "npm run typeorm migration:revert"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/connect-redis": "^0.0.23", "@types/express": "4.17.8", "@types/express-session": "1.17.0", "@types/lodash": "4.14.161", "@types/node": "14.11.1", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.0", "@types/uuid": "^8.3.0", "class-validator": "0.12.2", "nodemon": "^2.0.4", "ts-node": "^10.9.2", "typescript": "^4.8.2"}, "dependencies": {"@powersync/node": "^0.8.0", "@types/jsonwebtoken": "^9.0.9", "apollo-server-express": "2.17.0", "argon2": "^0.29.1", "bcryptjs": "^3.0.2", "connect-redis": "6.1.3", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.0.1", "express": "4.17.1", "express-session": "^1.17.1", "graphql": "^15.3.0", "https": "^1.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.0", "node-cron": "^3.0.3", "nodemailer": "^6.4.11", "pg": "^8.3.3", "reflect-metadata": "^0.1.13", "type-graphql": "^1.1.1", "typeorm": "^0.2.45", "uuid": "^8.3.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}}