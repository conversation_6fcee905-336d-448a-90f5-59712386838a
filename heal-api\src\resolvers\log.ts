import { Resolver, Query, Arg, Int, UseMiddleware } from "type-graphql";
import { LogQuery, LogEntry } from "../utils/logQuery";
import { isAuth } from "../middleware/isAuth";

@Resolver()
export class LogResolver {
  private logQuery = new LogQuery();

  @UseMiddleware(isAuth)
  @Query(() => [LogEntry])
  async getLogs(
    @Arg("startDate", { nullable: true }) startDate?: Date,
    @Arg("endDate", { nullable: true }) endDate?: Date,
    @Arg("level", { nullable: true }) level?: string,
    @Arg("companyId", () => Int, { nullable: true }) companyId?: number,
    @Arg("errorCode", { nullable: true }) errorCode?: string,
    @Arg("userId", () => Int, { nullable: true }) userId?: number
  ) {
    return this.logQuery.queryLogs({
      startDate,
      endDate,
      level,
      companyId,
      errorCode,
      userId,
    });
  }

  @UseMiddleware(isAuth)
  @Query(() => [LogEntry])
  async getTodaysErrors() {
    return this.logQuery.getTodaysErrors();
  }

  @UseMiddleware(isAuth)
  @Query(() => [LogEntry])
  async getCompanyErrors(
    @Arg("companyId", () => Int) companyId: number,
    @Arg("days", () => Int, { defaultValue: 7 }) days: number,
    @Arg("startDate", { nullable: true }) startDate?: Date,
    @Arg("endDate", { nullable: true }) endDate?: Date,
    @Arg("severity", { nullable: true }) severity?: string
  ) {
    // If startDate and endDate are provided, use them instead of days
    if (startDate && endDate) {
      return this.logQuery.queryLogs({
        startDate,
        endDate,
        companyId,
        level: "error",
        severity,
      });
    }

    // Otherwise use the days parameter
    const calculatedStartDate = new Date();
    calculatedStartDate.setDate(calculatedStartDate.getDate() - days);

    return this.logQuery.queryLogs({
      startDate: calculatedStartDate,
      companyId,
      level: "error",
      severity,
    });
  }
}
