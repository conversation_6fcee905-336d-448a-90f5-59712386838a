import {
  Resolver,
  Mutation,
  Query,
  ObjectType,
  Field,
  Int,
} from "type-graphql";

@ObjectType()
class SyncProgress {
  @Field() status: string;
  @Field(() => Int) recordsProcessed: number;
  @Field(() => Int, { nullable: true }) totalRecords?: number;
  @Field(() => Int) progress: number; // 0-100
  @Field({ nullable: true }) lastSyncTimestamp?: string;
}

@Resolver()
export class SyncResolver {
  /**
   * Query: Get the last PowerSync status (last sync timestamp if available)
   */
  @Query(() => String, { nullable: true })
  async lastPowerSyncStatus(): Promise<string | null> {
    try {
      // const result: any = await db.get(
      //   "SELECT MAX(updatedAt) as lastSync FROM SyncHistory WHERE status = ? OR status = ? OR status = ?",
      //   ["success", "in_progress", "failed"]
      // );
      const result = { lastSync: new Date().toISOString() };
      return result && result.lastSync ? result.lastSync : null;
    } catch (err) {
      return null;
    }
  }

  /**
   * Query: Get PowerSync progress for UI progress bar
   */
  @Query(() => SyncProgress)
  async powerSyncProgress(): Promise<SyncProgress> {
    // Get the latest in_progress or success sync
    // const latest: any = await db.get(
    //   "SELECT * FROM SyncHistory WHERE status = ? OR status = ? ORDER BY updatedAt DESC LIMIT 1",
    //   ["in_progress", "success"]
    // );
    const latest = {
      status: "in_progress",
      recordsProcessed: 500,
      lastSyncTimestamp: new Date().toISOString(),
    };
    if (!latest) {
      return { status: "idle", recordsProcessed: 0, progress: 0 };
    }
    // Placeholder: estimate total records (improve as needed)
    const totalRecords = 1000;
    let progress = 0;
    if (latest.status === "success") {
      progress = 100;
    } else if (latest.status === "in_progress" && totalRecords > 0) {
      progress = Math.min(
        99,
        Math.floor((latest.recordsProcessed / totalRecords) * 100)
      );
    }
    return {
      status: latest.status,
      recordsProcessed: latest.recordsProcessed,
      totalRecords,
      progress,
      lastSyncTimestamp: latest.lastSyncTimestamp,
    };
  }

  /**
   * Mutation: Force PowerSync to start syncing now (e.g., on user login or after token refresh)
   * This will reconnect PowerSync, so if the token was expired and is now renewed, sync will resume.
   */
  @Mutation(() => Boolean)
  async forcePowerSync(): Promise<boolean> {
    try {
      // await connectPowerSync();
      return true;
    } catch (err) {
      return false;
    }
  }
}
